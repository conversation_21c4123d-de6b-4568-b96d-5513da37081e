appId: com.lenskart.app

env:
  PINCODE: 546080
  UNIT_NO: 101
  NAME: Test
  EMAIL_ID: <EMAIL>
  COUNTRY: India
  PHONE_NO: 2123000005

---

#verifying Saved address page
- tapOn:
    id: "com.lenskart.app.store:id/tv_toolbar_title"

#tapping on Add new address
- tapOn: "Add New Address"

#Verifying user is on add new address page
- tapOn:
    id: "com.lenskart.app.store:id/tv_toolbar_title"


#Tapping on Save as option
- tapOn: "Save as:"

#Selecting on Work as an option
- tapOn: "Work"

#clear text in pincode
- tapOn:
    id: "com.lenskart.app:id/textinput_placeholder"
    optional: true

# Wait for field to be focused and auto-fill to complete
- waitForAnimationToEnd

# Clear any existing text using multiple methods
- eraseText
- inputText: ""
- eraseText

# Wait for clearing to complete
- waitForAnimationToEnd


#Tapping on pincode textfield
- tapOn: "Pincode*"

#Entering input in pincode field
- inputText: ${PINCODE}

# Wait for roadname to come
- waitForAnimationToEnd

#For roadname, we are skipping the step as it will automatically fetch from pincode

#Tapping on Unit no/House no
- tapOn: "Unit no / House no*"

#Entering input in Unit no
- inputText: ${UNIT_NO}


#Tapping on Name field
- tapOn: "Name*"

#clear text in Name
- tapOn:
    id: "com.lenskart.app.store:id/edt_input_text"
    index: 3
    optional: true

# Wait for field to be focused and auto-fill to complete
- waitForAnimationToEnd

# Clear auto-filled text using multiple methods
- eraseText
- inputText: ""
- eraseText

# Wait for clearing to complete
- waitForAnimationToEnd

#Entering Name
- inputText: ${NAME}

#tapping on Email textfield
- tapOn: "Email"

#Entering input in email id
- inputText: ${EMAIL_ID}

#clicking on dropdown
- tapOn:
    id: "com.lenskart.app.store:id/iv_dropdown"

#Verifying country list
- tapOn: "Choose Country Code"

#clicking on search icon
- tapOn:
     id: "com.lenskart.app:id/search_bar"

#clicking on search texfield for cursor
- tapOn:
    id: "com.lenskart.app:id/textinput_placeholder"

#calling country
- inputText: ${COUNTRY}

#Selecting India
#- tapOn:
    #    text: "India"
    #    index: 1

#-  inputText:
   # index: 5
  #  text: ""

#tapping on Phone no textfield
    # - tapOn:
    #  id: "com.lenskart.app.store:id/edt_input_text"
  #  index: 5

#calling phone no
# - inputText: ${PHONE_NO}

#Tapping on Save address button
- tapOn:
    id: "com.lenskart.app.store:id/button_primary"
