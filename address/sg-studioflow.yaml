appId: com.lenskart.app

env:
  FIRST_NAME: Test
  LAST_NAME: Test
  PHONE_NUMBER: "2123000006"  # Optional phone number
  EMAIL_ID: <EMAIL>
---

#On store page, verifying select a store text
- tapOn:
    id: "com.lenskart.app.store:id/tv_toolbar_title"

# Selecting the second store address
- tapOn:
    id: "com.lenskart.app.store:id/proceed_icon"
    index: 2

# Verifying Contact details text
- tapOn:
    id: "com.lenskart.app.store:id/tv_toolbar_title"

#verifying selected store
- tapOn:
    id: "com.lenskart.app.store:id/marker_pin"

# Clear autofilled contact details and enter new data

# Smart handling of First Name field (clear if autofilled, fill if blank)
- tapOn:
    text: "First Name*"
    optional: true
- eraseText
# Enter new first name
- inputText: ${FIRST_NAME}

# Smart handling of Last Name field (clear if autofilled, fill if blank)
- tapOn:
    text: "Last Name*"
    optional: true
- eraseText
# Enter new last name
- inputText: ${LAST_NAME}

#back to get back from keyboard
- tapOn:
    id: "com.android.systemui:id/back"

#scroll to see email id
- scrollUntilVisible:
    element:
      text: "Email ID"
    direction: DOWN

# Smart handling of Email field (clear if autofilled, fill if blank)
- tapOn:
    id: "com.lenskart.app.store:id/edt_input_text"
    index: 3
    optional: true
- eraseText
# Enter new email
- inputText: ${EMAIL_ID}

# Tap on Confirm Details button
- tapOn:
    text: "Confirm Details"
    optional: true

