appId: com.lenskart.app

---
# Handle Select Address with better error handling
- runFlow:
    when:
      visible: "Select Address"
    commands:
      - tapOn: "Select Address"

# Wait for address screen to load
- waitForAnimationToEnd

# Handle Add New Address option
- runFlow:
    when:
      visible: "Add New Address"
    commands:
      - tapOn: "Add New Address"

# Alternative: Handle if "Enter complete address" is directly visible
- runFlow:
    when:
      visible: "Enter complete address"
    commands:
      - tapOn: "Enter complete address"
- tapOn: "Flat no / House no / Building*"
- inputText: "Test Order"
- tapOn: "Area / Street / Sector / Village*"
- inputText: "Abc"
- tapOn: "Save address & Proceed"