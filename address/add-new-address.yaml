appId: com.lenskart.com

env:
  PINCODE: 122104
  HOUSENO: "Test House no and test building"

---
# User is on My Address screen and taps on Add Address cta
- tapOn: "Add New Address"
  #label: tap on Add New Address button

# tap on Change link on Google Map screen
- tapOn: "Change"
  #label: tap on Change link option displayed on Map screen

# tap on Add address manually link
- tapOn: "Add address manually"
  #label: tap on Add address manually link


# tap on Pincode placeholder displayed on Add delivery address screen
- tapOn: "Pincode*"
  #label: tap on Pincode placeholder

# delete the prefilled text in Pincode placeholder
- eraseText: 6
  #label: erase any text in the Pincode placeholder

# Add pincode text
- inputText: ${PINCODE}
  #label: Enter a valid pincode

# tap on House no Building name
- tapOn: "House no, Building name*"
  #label: Enter valid House and Building details

# Enter valid text as HouseNo and Building Name
- inputText: ${HOUSENO}

# Enter valid text in Road name text field
- tapOn: "Road name, Area, Locality*"
  #label: enter valid text in Road name and locality field

