appId: com.lenskart.app
---

#Tap on profile icon
- tapOn:
    id: "com.lenskart.app:id/iv_initials_holder"

#ORDERS
#Tap on Orders
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

#Validate My orders text on the Top
- assertVisible: "My orders"

#Click on back button
- tapOn: "Navigate up"

#WISHLIST
#Tap on Wishlist
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

#Validate Wishlist text on the Top
- assertVisible:
    text: "Wishlist"
    index: 0

#Click on back button
- tapOn: "Navigate up"

#NOTIFICATION
#Tap on Notification icon
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2

#Tap on No thanks
- tapOn: "No thanks"

#Click on back button
- tapOn:
    point: "7%,7%"

#Tap on Notification icon - 2nd time
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2

#Tap on No thanks
- tapOn: "No thanks"

#Validate Notification text on the top
- assertVisible: "Notification"

#Click on back button
- tapOn: "Navigate up"

#WALLET
#Tap on wallet icon
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 3

#Assert lenskart wallet text on the top
- assertVisible:
    id: "com.lenskart.app:id/image"

#Click on back button
- tapOn: "Navigate up"

#MY SAVED POWERS
#Tap on My saved powers
- tapOn:
    id: "com.lenskart.app:id/iv_end_icon"
    index: 0

#Validate text My saved powers on the top
- assertVisible: "My saved powers"

#Tap on back button
- tapOn: "Navigate up"

#MY FRAME SIZE
#Tap on My frame size
- tapOn:
    id: "com.lenskart.app:id/iv_end_icon"
    index: 1

#Validate text available sizes for collection
- assertVisible: "Available Sizes in Collection"

#Click on back button
- tapOn: "Navigate up"

#Scroll slow
- scrollUntilVisible:
    element: "View Gift Card Balance"
    direction: down


#AR-TRY on
#Tap on AR-Try on
- tapOn:
    id: "com.lenskart.app:id/iv_end_icon"
    index: 2
#Give camera permission
- runFlow:
    when:
      visible: 'While using the app'
    commands:
      - tapOn: "While using the app"
- back

#3-D Models
#Tap on 3-D models
- tapOn:
    id: "com.lenskart.app:id/iv_end_icon"
    index: 3

#Validate text My Saved 3D on top
- assertVisible: "My saved 3D"

#Tap on back button
- tapOn: "Navigate up"

#Scoreboard
#Tap on scoreboard
- tapOn:
      id: "com.lenskart.app:id/iv_end_icon"
      index: 4

#Validate Scoreboard
- assertVisible:
      text: "Scoreboard"
      index: 0

#Tap on back button
- tapOn:
      point: "7%,7%"

#Gift-Cards
#Tap on Claim gift card
- tapOn:
    id: "com.lenskart.app:id/iv_end_icon"
    index: 5

#Validate text on the top Claim gift card
- assertVisible:
      text: "Claim Gift Card"
      index: 0

#Click on back button
- tapOn:
      point: "7%,7%"


#Tap on View Gift Card Balance
- tapOn:
    id: "com.lenskart.app:id/iv_end_icon"
    index: 6

#Validate Gift Card balance
- assertVisible:
      text: "Gift card Balance"
      index: 0

#Tap on back button
- tapOn:
      point: "7%,7%"

- scrollUntilVisible:
    element: "Find nearby store"
    direction: down

#Tap on eyetest and Frame trail Home
- tapOn: "Eye test & frame trial at home"


#Validate Lenskart at Home
- assertVisible: "Lenskart at Home"

#Tap on back button
- tapOn:
    point: "11%,7%"

#Tap on leave button
- tapOn: "Leave"

#Tap on back button
- tapOn:
    point: "11%,7%"

#Tap on Find nearby Store
- tapOn: "Find nearby store"

- tapOn: "No thanks"

#Go back to profile page
- back

 #Click on chat with eyewear expert
- tapOn: "Chat with an eyewear Expert"

#To give location permission
- runFlow:
    when:
      visible: 'While using the app'
    commands:
      - tapOn: "While using the app"
- back

#Scroll down untill Call an eyewear is visible
- scrollUntilVisible:
    element: "Call an eyewear Expert"
    direction: down


#Tap on Call an eyewear expert
- tapOn: "Call an eyewear Expert"

#To give location permission
- runFlow:
    when:
      visible: 'While using the app'
    commands:
      - tapOn: "While using the app"

- back

#Tap on addresses
- tapOn: "My addresses"

#Validate my addresses text on above
- assertVisible: "My addresses"

#Tap on back button
- tapOn:
    id: "com.lenskart.app.store:id/cta_back"

#Tap on My notifications
- tapOn: "My notifications"

#Validate the Notification text on the top
- assertVisible: "Notification"

#Tap on back button
- tapOn: "Navigate up"

#Scroll untill Logout is visible
- scrollUntilVisible:
    element: "Logout"
    direction: down

#Tap on Manage notifications
- tapOn: "Manage notifications"

#Assert the text Manage notifications
- assertVisible: "Manage Notifications"

#Click on back button
- tapOn:
    point: "7%,7%"

#Tap on Frequently asked questions
- tapOn: "Frequently asked questions"

#Assert the tet FAQ's
- assertVisible: "Frequently Asked Questions"

#Click on back button
- tapOn:
    point: "7%,7%"

#Tap on Contact us
- tapOn: "Contact us"

#Tap on accept all cookies
- tapOn: "Accept All Cookies"

#Assert the Lenskart texts
- assertVisible:
    text: "Lenskart"
    index: 0

#Click on back button
- tapOn:
    point: "7%,7%"

#Tap on about lenskart
- tapOn: "About Lenskart"

#Assert text About lenskart
- assertVisible: "About Lenskart"

#Click on back button
- tapOn:
    point: "7%,7%"

#Tap on More options
- tapOn: "More options"

#Assert the text About Lenskart
- assertVisible: "About Lenskart"

#Click on back button
- tapOn:
    point: "7%,7%"

#Tap on Logout
- tapOn: "Logout"








