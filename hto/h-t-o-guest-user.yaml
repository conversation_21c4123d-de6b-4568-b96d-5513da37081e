appId: com.lenskart.app

env:
  PINCODE: "122002"
  HOUSENO: "Test House"
  LANDMARK: "123"
  MOBILENO: 2123000009
  OTP: 7777

---
- clearState



- launchApp

- tapOn: "Skip"
- tapOn: "Go to Home"

- runFlow:
    when:
      visible: 'Allow'
    commands:
      - tapOn: "Allow"
- runFlow:
    when:
      visible: 'While using the app'
    commands:
      - tapOn: "While using the app"

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- tapOn: "Book Appointment"

- assertVisible: "Login or signup"


- tapOn:
    id: "com.lenskart.app:id/tiet_text_input"

- inputText: ${MOBILENO}

- tapOn:
    id: "com.lenskart.app:id/btn_get_otp"

- tapOn:
    id: "com.lenskart.app:id/et_otp"

- inputText: ${OTP}

- tapOn: "Add Address"
- tapOn: "Change"

- tapOn:
    id: "com.lenskart.app:id/edtManualSearch"

- inputText:
    text: ${PINCODE}

- tapOn:
    id: "android:id/text1"
    index: 0

- tapOn: "Confirm Appointment Location"

- tapOn:
    id: "com.lenskart.app:id/edtHouseAndFlat"

- inputText:
    text: ${HOUSENO}

- tapOn:
    id: "com.lenskart.app:id/edtRoadAreaLandmark"

- inputText:
    text: ${LANDMARK}

- tapOn:
    id: "com.lenskart.app:id/button_container"

- assertVisible:
    id: "com.lenskart.app:id/bottom_cardview"

- tapOn:
    id: "com.lenskart.app:id/bottom_cardview"

- assertVisible:
    id: "com.lenskart.app:id/tv_order_id"




