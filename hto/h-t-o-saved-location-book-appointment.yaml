
- tapOn: "Try At Home"
- tapOn: "Change"
- tapOn:
    id: "com.lenskart.app.store:id/main_container"
    index: 1
- tapOn: "Proceed to date & time"
# Select the Number off people

- tapOn:
      id: "com.lenskart.app:id/layout_date"
      index: 1
#Choose Date

- tapOn:
      id: "com.lenskart.app:id/layout_date"
      index: 4

- scroll

#- scrollUntilVisible:
#      element:
#          text: "09:00PM"
#     direction: DOWN
#It selects the Time
- tapOn: "10:30 AM"

- tapOn:
      id: "com.lenskart.app:id/button_container"

#It will navigate back to Homepage.

- tapOn: "Navigate up"
