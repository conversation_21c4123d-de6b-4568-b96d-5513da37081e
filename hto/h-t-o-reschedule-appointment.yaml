appId: com.lenskart.app

---

- launchApp

- runFlow: ../login/login1.yaml

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1


- tapOn: "Reschedule"
  
- tapOn:
    id: "com.lenskart.app:id/tv_time"
    index: 0
- tapOn:
    id: "com.lenskart.app:id/button_container"
- tapOn:
    id: "com.lenskart.app:id/radio_button"
    index: 0
- tapOn:
    id: "com.lenskart.app:id/button_container"

- assertVisible: "Appointment Rescheduled"
- tapOn: "Cancel Appointment"
- tapOn:
    id: "com.lenskart.app:id/radio_button"
    index: 0
- tapOn: "Continue"
- assertVisible: "Your order has been cancelled successfully!"

