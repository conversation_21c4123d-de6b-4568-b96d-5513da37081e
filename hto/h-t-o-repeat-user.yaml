appId: com.lenskart.app

env:
  PINCODE: "846003"
  PINCODE_HTO: "122002"
  HOUSENO: "Test Test"
  LANDMARK: "Test"

---

- runFlow: ../login/Popup.yaml

- runFlow : ../login/handle-permissions.yaml

- tapOn:
    id: "com.lenskart.app:id/home_express_delivery"

- tapOn:
    id: "com.lenskart.app:id/top_search_bar"

- inputText:
    text: ${PINCODE_HTO}

- tapOn:
    id: "com.lenskart.app.store:id/cl_container"
    index: 0


- tapOn: "Try At Home"

#Verify user lands on HTO Page successfully
- assertVisible:
    "Lenskart at Home"

- tapOn: "Change"
- tapOn: "Add Address"
- tapOn: "Change"

- tapOn:
    id: "com.lenskart.app:id/edtManualSearch"

- inputText:
    text: ${PINCODE}

- tapOn:
    id: "android:id/text1"
    index: 0

- tapOn: "Confirm Appointment Location"

- tapOn:
    id: "com.lenskart.app:id/edtHouseAndFlat"

- inputText:
    text: ${HOUSENO}

- tapOn:
    id: "com.lenskart.app:id/edtRoadAreaLandmark"

- inputText:
    text: ${LANDMARK}

- tapOn:
    id: "com.lenskart.app:id/button_container"

#Verify pincode is unservicable
- assertVisible:
    "No slots available at your location.Try with another location"

- tapOn: "Change"
- tapOn:
    id: "com.lenskart.app.store:id/main_container"
    index: 0

- tapOn: "Proceed to date & time"

#Verify button and banner  visible on slot screen

- assertVisible:
    id: "com.lenskart.app:id/iv_logo_2"
- assertVisible:
    id: "com.lenskart.app:id/button_container"

- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 0

#Verify amount for 1 user
- assertVisible:
    "₹0"

- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 1

#Verify amount for 2 users
- assertVisible:
    "₹0"
- tapOn:
    id: "com.lenskart.app:id/button_container"

#Verify order placed successfully
- assertVisible:
    "Home Eye Checkup"


