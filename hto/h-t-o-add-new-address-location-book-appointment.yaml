appId: com.lenskart.app

env:
  PINCODE: "122002"
  HOUSENO: "Test House"
  LANDMARK: "123"

---

- launchApp

- runFlow: ../login/login1.yaml

- tapOn: "Try At Home"

- tapOn: "Change"
- tapOn: "Add Address"
- tapOn: "Change"

- tapOn:
    id: "com.lenskart.app:id/edtManualSearch"

- inputText:
    text: ${PINCODE}

- tapOn:
    id: "android:id/text1"
    index: 0

- tapOn: "Confirm Appointment Location"

- tapOn:
    id: "com.lenskart.app:id/edtHouseAndFlat"

- inputText:
    text: ${HOUSENO}

- tapOn:
    id: "com.lenskart.app:id/edtRoadAreaLandmark"

- inputText:
    text: ${LANDMARK}

- tapOn:
    id: "com.lenskart.app:id/button_container"

- assertVisible:
    id: "com.lenskart.app:id/bottom_cardview"

- tapOn:
    id: "com.lenskart.app:id/bottom_cardview"

- assertVisible:
    id: "com.lenskart.app:id/tv_order_id"

