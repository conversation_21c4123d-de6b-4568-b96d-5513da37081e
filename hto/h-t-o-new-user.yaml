appId: com.lenskart.app

env:
  PINCODE: "122002"
  HOUSENO: "Test Test"
  LANDMARK: "Test"

---

- runFlow: ../login/Login-hto-new-user.yaml

- runFlow : ../login/handle-permissions.yaml

- tapOn:
    id: "com.lenskart.app:id/home_express_delivery"

- tapOn:
    id: "com.lenskart.app:id/top_search_bar"

- inputText:
    text: ${PINCODE}

- tapOn:
    id: "com.lenskart.app.store:id/cl_container"
    index: 1


- tapOn: "Try At Home"

#Verify user lands on HTO Page successfully
- assertVisible:
    "Eye Test at Home"

- tapOn: "Book Appointment"

- tapOn: "Proceed to date & time"

#Verify button visible on slot screen
- assertVisible:
    id: "com.lenskart.app:id/button_container"

- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 0

#Verify amount for 1 user
- assertVisible:
    "₹99"

- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 1

#Verify amount for 2 users
- assertVisible:
    "₹198"

