appId: com.lenskart.app

---


- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD : 224271 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as 224271

#- tapOn:
#    id: "com.lenskart.app:id/tvProductId"
- tapOn: "Image"

#- tapOn:
#    id: "com.lenskart.app:id/image"
#    index: 0

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/c-l-enter-power-manually.yaml

- runFlow: ../../../address/saved-address.yaml

- scrollUntilVisible:
    element:
      text: "ShopBack"
    direction: DOWN

- runFlow: ../../../payment/sg-shopback-payment.yaml