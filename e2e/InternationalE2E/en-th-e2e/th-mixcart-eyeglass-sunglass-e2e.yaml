appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

- tapOn:
    id: "com.lenskart.app:id/iv_icon"
    index: 0

- assertVisible: "TRENDING AT LENSKART"

- tapOn: "eyeglasses"

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- tapOn: "Add to Cart"

- runFlow:
    file: ../../../progressive/optimized-progressive.yaml
    env:
      lensType: "Non Anti-Glare Progressive"

- tapOn: "Proceed to Cart"

- runFlow: ../../../addPower/add-power-submit-later.yaml

- waitForAnimationToEnd

#back from cart
- tapOn:
    point: "6%,6%"

- tapOn: "Navigate up"
- tapOn:
    id: "com.lenskart.app:id/iv_back"


#tap on search icon from plp
- tapOn:
    id: "com.lenskart.app:id/iv_icon"
    index: 0

#select sunglasses from trending at lenskart
- tapOn: "sunglasses"

- runFlow: ../../../plp/international-plp.yaml

- runFlow: ../../../BitzFlow/with-widget-select-bitz.yaml

#Continue from cart and navigate to address
- runFlow: ../../../address/th-saved-address.yaml

- tapOn: "Add Credit/ Debit/ ATM cards"

#payment
- runFlow: ../../../payment/international-c-c-details.yaml
