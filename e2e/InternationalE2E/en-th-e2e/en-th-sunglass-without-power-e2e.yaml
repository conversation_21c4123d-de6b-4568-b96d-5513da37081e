appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

#Search keyword by using search icon
- runFlow: ../../../search/international-search-icon.yaml

#Search keyword
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Sunglasses

#clicking on sunglasses text
- tapOn:
    text: "sunglasses"
    index: 1

#clicking on first product/sunglass from the result
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

#add to cart
- tapOn: "Add to Cart"

#address
- runFlow: ../../../address/th-saved-address.yaml

#payment
- runFlow: ../../../payment/th-promptpay-payment.yaml



