appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

#Search anything using Search icon
- runFlow : ../../../search/international-search-icon.yaml

#searching PID
- runFlow: ../../../search/th-search-pid.yaml

#add to cart
- tapOn: "Add to Cart"

- runFlow:
    file: ../../../power/optimized-eyeglass-with-power.yaml
    env:
      lensType: "Transition"

- tapOn: "Proceed to Cart"

#add coating if visible
- runFlow:
    when:
      visible: "Add a Coating"
    commands:
      - tapOn:
          id: "com.lenskart.app:id/coating_header"
      - tapOn: "Continue to Cart"

# add power by entering manually
- runFlow: ../../../addPower/add-power-enter-manully.yaml

#address
- runFlow: ../../../address/th-saved-address.yaml

#payment
- runFlow: ../../../payment/th-promptpay-payment.yaml











