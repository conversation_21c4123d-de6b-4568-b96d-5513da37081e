appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

#Search anything using Search icon
- runFlow : ../../../search/international-search-icon.yaml

#Search keyword
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: lenskart bitz eyeglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as lenskart bitz eyeglasses

#clicking on first suggestion from the result
- tapOn:
    text: "lenskart bitz eyeglasses"
    index: 1
#clicking on first product
- runFlow: ../../../plp/international-plp.yaml

#selecting bitz
- runFlow: ../../../BitzFlow/with-button-select-bitz.yaml

#selecting frame only
- runFlow: ../../../power/frame-only.yaml

#address
- runFlow: ../../../address/th-saved-address.yaml

#payment
- runFlow: ../../../payment/th-promptpay-payment.yaml








