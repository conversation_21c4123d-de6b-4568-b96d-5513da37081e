appId: com.lenskart.app

---

- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml


- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD : Sunglass # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Sunglass

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1


- tapOn: "Add to Cart"


- runFlow: ../../../power/without-power-sunglass.yaml

- runFlow: ../../../address/saved-address.yaml

- runFlow: ../../../payment/international-c-c-details.yaml

