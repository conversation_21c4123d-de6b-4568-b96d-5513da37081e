appId: com.lenskart.app

---
- runFlow: ../../../login/optimized-login.yaml
- runFlow: ../../../login/handle-permissions.yaml
- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : lens spherical # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as lens spherical

- runFlow: ../../../plp/uae-plp.yaml
- runFlow: ../../../clPowerType/c-l-upload-prescription .yaml
- tapOn:
    id: "com.lenskart.app:id/layout_camera"
- runFlow:
    when:
      visible: 'While using the app'
    commands:
      - tapOn: "While using the app"
- tapOn:
    id: "com.sec.android.app.camera:id/normal_center_button"
- tapOn:
    text: "OK"
    index: 1
- runFlow: ../../../pdp/uae-pdp.yaml
- tapOn: "Select Address"
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/international-c-c-details.yaml
