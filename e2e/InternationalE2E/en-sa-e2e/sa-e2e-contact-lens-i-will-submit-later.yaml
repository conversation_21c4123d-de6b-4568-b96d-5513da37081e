appId: com.lenskart.app

---

- runFlow: ../../../login/optimized-login.yaml
- runFlow: ../../../login/handle-permissions.yaml

- scrollUntilVisible:
     element:
        text: "Contact Lenses"          # or use textMatches
     direction: DOWN
     speed: 60
     timeout: 20000
     visibilityPercentage: 50
     waitToSettleTimeoutMs: 500
     centerElement: true
- runFlow: ../../../home/<USER>
- runFlow: ../../../plp/sa-plp.yaml
- runFlow: ../../../clPowerType/c-l-i-will-submit-power-later.yaml
- tapOn: "Select Address"
- runFlow: ../../../clPowerType/uae-cl-submit-power-later.yaml
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/international-c-c-details.yaml

