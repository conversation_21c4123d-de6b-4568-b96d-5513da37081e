appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: 224465 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as PID

#click on product
- tapOn:
    id: "com.lenskart.app:id/tvProductId"

- runFlow: ../../../pdp/sg-bitz-widget/sg-with-button-select-bitz.yaml

- runFlow: ../../../pdp/sg-package-screen/sg-sunglass-without-power.yaml

- runFlow: ../../../cart/sg-cart/sg-regular-product-cart.yaml

- runFlow: ../../../address/sg-saved-address.yaml

- runFlow: ../../../payment/sg-atome-payment.yaml