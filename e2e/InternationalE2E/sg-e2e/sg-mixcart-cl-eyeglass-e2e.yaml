appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

#search cl
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD : 224272 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as 224272

#choose first product
- tapOn:
    id: "com.lenskart.app:id/tvProductId"

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/c-l-enter-power-manually.yaml

- runFlow:
    when:
      visible: "Add Contact Lens Solution"
    file: ../../../pdp/add-cl-solution.yaml

#back from cart
- tapOn:
    point: "6%,6%"

- waitForAnimationToEnd

#back from last pdp
- tapOn:
    id: "com.lenskart.app:id/iv_back"

#trending at lenskart
- assertVisible: "TRENDING AT LENSKART"

#select product
- tapOn: "zero power computer glasses"

#select first product from plp
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

#add to cart
- tapOn: "Add to Cart"

- waitForAnimationToEnd

#tapping on select address button
- tapOn:
    id: "com.lenskart.app:id/btn_continue"


- runFlow:  ../../../address/sg-saved-address.yaml

- runFlow: ../../../payment/sg-shopback-payment.yaml