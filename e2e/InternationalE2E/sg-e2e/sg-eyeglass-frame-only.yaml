appId: com.lenskart.app

---

- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml


- runFlow:
      file: ../../../search/international-search-keyword.yaml
      env:
          SEARCHKEYWORD : Eyeglass # <-- Run commands from "searchkeyword.yaml"
          label: Search keywrod passed as Eyeglass
- pressKey: Enter

- tapOn:
      id: "com.lenskart.app:id/image"
      index: 1

#select lenses
- tapOn: "Select Lenses"

#select frame only
- tapOn:
    text: "Frame Only"
    optional: true


# Handle address selection
- tapOn: "Select Address"

- runFlow: ../../../address/saved-address.yaml

- runFlow: ../../../payment/international-c-c-details.yaml

