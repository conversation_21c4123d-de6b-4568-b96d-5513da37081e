appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: Eyeglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Eyeglasses

#clicking on eyeglasses text
- tapOn:
    text: "eyeglasses"
    index: 1

#select first product on plp
- runFlow: ../../../plp/international-plp.yaml

- runFlow: ../../../pdp/sg-package-screen/sg-eyeglass-with-power.yaml

- runFlow:
    file: ../../../power/optimized-eyeglass-with-power.yaml
    env:
      lensType: "Premium Anti-Glare"

- runFlow: ../../../addPower/add-power-saved-powers.yaml

- runFlow: ../../../cart/sg-cart/sg-regular-product-cart.yaml

- runFlow: ../../../address/sg-saved-address.yaml

- runFlow: ../../../payment/sg-atome-payment.yaml