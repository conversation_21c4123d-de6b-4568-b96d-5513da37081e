appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

- runFlow:
      file: ../../../search/international-search-keyword.yaml
      env:
          SEARCHKEYWORD : 224271 # <-- Run commands from "searchkeyword.yaml"
          label: Search keywrod passed as 224271

- tapOn:
      id: "com.lenskart.app:id/tvProductId"

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/c-l-enter-power-manually.yaml

- runFlow:
      when:
          visible: "Add Contact Lens Solution"
      file: ../../../pdp/add-cl-solution.yaml

#tapping on select address button
- tapOn:
      id: "com.lenskart.app:id/btn_continue"

- runFlow: ../../../address/sg-saved-address.yaml

- runFlow: ../../../payment/sg-shopback-payment.yaml
