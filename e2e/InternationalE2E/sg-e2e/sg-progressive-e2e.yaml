appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

- runFlow: ../../../pdp/sg-pdp/sg-progressive-pdp.yaml

- runFlow: ../../../pdp/sg-package-screen/sg-eyeglass-progressive.yaml

- runFlow:
    file: ../../../progressive/optimized-progressive.yaml
    env:
      lensType: "Tokai Progressive"

- tapOn: "Skip coating addition"

- runFlow: ../../../cart/sg-cart/sg-progressive-product-cart.yaml

- runFlow: ../../../address/sg-studioflow.yaml

- runFlow: ../../../payment/sg-grabpay-payment.yaml