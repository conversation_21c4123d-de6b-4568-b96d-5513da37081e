appId: com.lenskart.app

---
- launchApp
- tapOn: "Skip"
- tapOn: "Go to Home"
- runFlow:
    when:
      visible: 'Allow'
    commands:
      - tapOn: "Allow"
- runFlow:
    when:
      visible: 'While using the app'
    commands:
      - tapOn: "While using the app"
- tapOn:
    id: "com.lenskart.app:id/item_selection_ll"
    index: 2
- tapOn: "Book Appointment"

- tapOn: "Enter 10-digit phone number"
- inputText: "1000000000"
- tapOn: "Get OTP"
- tapOn: "Enter OTP"
- inputText: "77 77"

- tapOn:
    id: "com.lenskart.app.store:id/main_container"
    index: 0
- tapOn: "Proceed to date & time"
- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 1
#Choose Date

- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 4

#It selects the Time
- tapOn: "01:00 AM"

- scrollUntilVisible:
    element:
      text: "07:00PM"
    direction: DOWN

- tapOn:
    id: "com.lenskart.app:id/button_container"

#It will navigate back to Homepage.

- tapOn: "Navigate up"
