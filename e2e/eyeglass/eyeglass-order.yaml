appId: com.lenskart.app
env:
    MOBILENO: 2123000000
    OTP: 7777
---
- clearState

- launchApp

- extendedWaitUntil:
    visible: "Login or signup" # or any other selector
    timeout: 25000      # Timeout in milliseconds
    

- tapOn:
    id: "com.lenskart.app:id/tiet_text_input"

- inputText: ${MOBILENO}

- tapOn:
    id: "com.lenskart.app:id/btn_get_otp"

- tapOn:
    id: "com.lenskart.app:id/et_otp"

- inputText: ${OTP}

# Tap on Go to home CTA
- tapOn:
    id: "com.lenskart.app:id/btnCta"
   

# Tap on Search bar placeholder on the homepage
- tapOn:
    id: "com.lenskart.app:id/tv_label"

- tapOn:
    id: "com.lenskart.app:id/edtSearch"

# Search Query
- inputText: chasma

# Tap on Search result
- tapOn:
    id: "com.lenskart.app:id/query"
    index: 0


# PLP screen
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

# Tap on Add to cart CTA
- tapOn:
    id: "com.lenskart.app:id/btn_add_to_cart"

# Tap on With Power type
- tapOn:
    id: "com.lenskart.app:id/cv_power_type"
    index: 0

# Tap on Anti-glare premium
- tapOn:
    id: "com.lenskart.app:id/bottom_bg"
    index: 0

# Tap on Saved Power View All
- tapOn:
    id: "com.lenskart.app:id/view_all_text_button_tv"

# Tap on first saved power option
- tapOn:
    id: "com.lenskart.app:id/iv_glass"
    index: 0

# Tap on Gold Membership Thanks overlay
- runFlow:
    when:
      visible: 'Go to Home'
    commands:
        - tapOn: 'Thanks!'

# Scroll down to view Offers header/section on Cart screen 
- scrollUntilVisible:
    element:
     text: "Offers" # or any other selector
    direction: DOWN # DOWN|UP|LEFT|RIGHT (optional, default: DOWN)
    timeout: 50000 # (optional, default: 20000) ms
    speed: 40 # 0-100 (optional, default: 40) Scroll speed. Higher values scroll faster.
    visibilityPercentage: 100 # 0-100 (optional, default: 100) Percentage of element visible in viewport
    centerElement: false # true|false (optional, default: false)






# Tap on Select Address CTA on Cart screen
- tapOn: "Select address"

# If any address exist on Address screen, then tap on Proceed icon
- runFlow:
    when:
      visible: 'com.lenskart.app.store:id/proceed_icon'
    commands:
        - tapOn: 'com.lenskart.app.store:id/proceed_icon'

        


