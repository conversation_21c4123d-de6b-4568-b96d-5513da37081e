appId: com.lenskart.app

---
- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Withpower Eyeglass # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Withpower Eyeglass

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#Eyeglass will be added to cart.

- tapOn: "Add to Cart"
#It will select the Eyeglass- Owndays japan clear vision lenses with power package.
- runFlow:
    file: ../../power/optimized-eyeglass-with-power.yaml
    env:
     lensType: "Owndays japan clear vision lenses"

- runFlow: ../../addPower/add-power-upload-prescription.yaml

- runFlow: ../../address/saved-address.yaml

- runFlow: ../../payment/credit-card-details.yaml

- runFlow: ../../payment/payment-retry.yaml



