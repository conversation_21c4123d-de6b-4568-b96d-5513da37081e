appId: com.lenskart.app
---
- clearState

- launchApp

- tapOn:
      id: "com.lenskart.app:id/tiet_text_input"

- inputText: 2123000000

- tapOn:
      id: "com.lenskart.app:id/btn_get_otp"

- tapOn:
      id: "com.lenskart.app:id/et_otp"

- inputText: 7777

# Verify if on Go to home screen is visible. If yes then click (Contional statement)
- runFlow:
      when:
          visible: 'Go to Home'
      commands:
          - tapOn: 'Go to Home'


- scrollUntilVisible:
      element:
          text: "Eyeglasses" # or any other selector
      direction: DOWN # DOWN|UP|LEFT|RIGHT (optional, default: DOWN)
      timeout: 50000 # (optional, default: 20000) ms
      speed: 40 # 0-100 (optional, default: 40) Scroll speed. Higher values scroll faster.
      visibilityPercentage: 100 # 0-100 (optional, default: 100) Percentage of element visible in viewport
      centerElement: false # true|false (optional, default: false)

- tapOn:
      point: "14%,36%"