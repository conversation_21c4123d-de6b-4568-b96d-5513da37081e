appId: com.lenskart.app
---

# Subglass PLP screen - tap on first product
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
    label: tap on first product on Sunglass PLP page

# Tap on Add to cart CTA on PDP screen
- tapOn:
    id: "com.lenskart.app:id/btn_add_to_cart"
    label: tap on Add to cart CTA on PDP screen


# Add Lens Details screen
- assertVisible: 'Select your Power Type:'


# Tap on With Power type
- tapOn:
    id: "com.lenskart.app:id/cv_power_type"
    index: 1
    label: tap on With Power CTA

# Tap on Brown tinted Power Lenses
- tapOn:
    id: "com.lenskart.app:id/bottom_bg"
    index: 0
    label: tap on Brown tinted power lens option

# Tap on Saved Power View All
- tapOn:
    id: "com.lenskart.app:id/view_all_text_button_tv"
    label: tap on View All icon to view Saved Powers

# Tap on first saved power option
- tapOn:
    id: "com.lenskart.app:id/iv_glass"
    index: 0
    label: tap on first Saved power option


      # Scroll down to view Offers header/section on Cart screen
      # - scrollUntilVisible:
      # element:
    # text: "Offers" # or any other selector
    # direction: DOWN # DOWN|UP|LEFT|RIGHT (optional, default: DOWN)
    # timeout: 50000 # (optional, default: 20000) ms
    # speed: 40 # 0-100 (optional, default: 40) Scroll speed. Higher values scroll faster.
    # visibilityPercentage: 100 # 0-100 (optional, default: 100) Percentage of element visible in viewport
    # centerElement: false # true|false (optional, default: false)


# Tap on Proceed to Payment button
- tapOn:
    id: "com.lenskart.app:id/btn_continue"
    label: tap on Proceed to Payment button