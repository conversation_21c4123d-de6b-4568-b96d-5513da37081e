appId: com.lenskart.app

---

- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Withpower sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Withpower sunglasses

#It will select the 1st product from Sunglasses PLP

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#Sunglasses will be added to cart.

- tapOn: "Add to Car<PERSON>"
#It will select the sunglasses - Pink Tinted with power package.
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Pink Tinted"

- runFlow: ../../addPower/add-power-enter-manully.yaml

- runFlow: ../../address/saved-address.yaml

- runFlow: ../../payment/credit-card-details.yaml
