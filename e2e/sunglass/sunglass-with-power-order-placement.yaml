appId: com.lenskart.app
---
# Login function is called here
- runFlow: login.yaml # <-- Run commands from "login.yaml"

# Search Keyword function
- runFlow:
    file: searchkeyword.yaml
    env:
      SEARCHKEYWORD : Sunglass # <-- Run commands from "searchkeyword.yaml"

# SUNGLASS WITH POWER ORDER PLACEMENT
- runFlow: sunglasses-with-power-saved-power-reusable-method.yaml



- runFlow: Credit_Card_Details.yaml

# Tap on Save and Pay Now button on Payments screen


# Tap on INR to select Rupee Payment option - PG screen
- tapOn: "INR"






