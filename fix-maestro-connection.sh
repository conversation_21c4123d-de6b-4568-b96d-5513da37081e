#!/bin/bash

#===============================================================================
# Maestro Connection Fix Script
# Diagnoses and fixes common Maestro connection issues
#
# Usage:
#   ./fix-maestro-connection.sh                    # Run all diagnostic checks and fixes
#   ./fix-maestro-connection.sh --check-only       # Only run diagnostics, no fixes
#   ./fix-maestro-connection.sh --force-restart    # Force restart all services
#   ./fix-maestro-connection.sh --help             # Show help
#===============================================================================

set +e  # Continue execution even if commands fail

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
CHECK_ONLY=false
FORCE_RESTART=false
VERBOSE=false

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --check-only)
            CHECK_ONLY=true
            ;;
        --force-restart)
            FORCE_RESTART=true
            ;;
        --verbose)
            VERBOSE=true
            ;;
        --help)
            echo "Maestro Connection Fix Script"
            echo ""
            echo "Usage:"
            echo "  ./fix-maestro-connection.sh                    # Run all diagnostic checks and fixes"
            echo "  ./fix-maestro-connection.sh --check-only       # Only run diagnostics, no fixes"
            echo "  ./fix-maestro-connection.sh --force-restart    # Force restart all services"
            echo "  ./fix-maestro-connection.sh --verbose          # Verbose output"
            echo "  ./fix-maestro-connection.sh --help             # Show this help"
            echo ""
            echo "This script diagnoses and fixes common Maestro connection issues including:"
            echo "  - ADB server problems"
            echo "  - Device connectivity issues"
            echo "  - Port conflicts"
            echo "  - Maestro installation problems"
            echo "  - App installation verification"
            exit 0
            ;;
    esac
done

echo -e "${BLUE}🔧 Maestro Connection Diagnostic & Fix Tool${NC}"
echo -e "${YELLOW}📅 $(date '+%Y-%m-%d %H:%M:%S')${NC}"
if [ "$CHECK_ONLY" = true ]; then
    echo -e "${YELLOW}🔍 Mode: Diagnostic Only (no fixes will be applied)${NC}"
elif [ "$FORCE_RESTART" = true ]; then
    echo -e "${YELLOW}🔄 Mode: Force Restart All Services${NC}"
else
    echo -e "${YELLOW}🛠️  Mode: Diagnostic + Auto-Fix${NC}"
fi
echo ""

# Function to log verbose messages
log_verbose() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${CYAN}[VERBOSE] $1${NC}"
    fi
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check ADB installation and version
check_adb() {
    echo -e "${BLUE}🔍 Checking ADB Installation...${NC}"
    
    if command_exists adb; then
        local adb_version=$(adb version 2>/dev/null | head -n1)
        echo -e "${GREEN}✅ ADB found: $adb_version${NC}"
        log_verbose "ADB path: $(which adb)"
        return 0
    else
        echo -e "${RED}❌ ADB not found in PATH${NC}"
        echo -e "${YELLOW}💡 Install Android SDK Platform Tools or add ADB to PATH${NC}"
        return 1
    fi
}

# Function to check Maestro installation
check_maestro() {
    echo -e "${BLUE}🔍 Checking Maestro Installation...${NC}"
    
    if command_exists maestro; then
        local maestro_version=$(maestro --version 2>/dev/null || echo "Unknown version")
        echo -e "${GREEN}✅ Maestro found: $maestro_version${NC}"
        log_verbose "Maestro path: $(which maestro)"
        return 0
    else
        echo -e "${RED}❌ Maestro not found in PATH${NC}"
        echo -e "${YELLOW}💡 Install Maestro: curl -Ls \"https://get.maestro.dev\" | bash${NC}"
        return 1
    fi
}

# Function to restart ADB server
restart_adb_server() {
    echo -e "${BLUE}🔄 Restarting ADB Server...${NC}"
    
    log_verbose "Killing ADB server..."
    adb kill-server >/dev/null 2>&1
    sleep 2
    
    log_verbose "Starting ADB server..."
    adb start-server >/dev/null 2>&1
    sleep 3
    
    echo -e "${GREEN}✅ ADB server restarted${NC}"
}

# Function to check device connectivity
check_device_connectivity() {
    echo -e "${BLUE}🔍 Checking Device Connectivity...${NC}"
    
    local devices_output=$(adb devices 2>/dev/null)
    local device_count=$(echo "$devices_output" | grep -c "device$" || echo "0")
    
    if [ "$device_count" -gt 0 ]; then
        echo -e "${GREEN}✅ Found $device_count connected device(s)${NC}"
        echo "$devices_output" | grep "device$" | while read -r line; do
            local device_id=$(echo "$line" | awk '{print $1}')
            echo -e "${YELLOW}   📱 Device: $device_id${NC}"
        done
        return 0
    else
        echo -e "${RED}❌ No devices found${NC}"
        echo -e "${YELLOW}💡 Make sure:${NC}"
        echo -e "${YELLOW}   - Device is connected via USB${NC}"
        echo -e "${YELLOW}   - USB Debugging is enabled${NC}"
        echo -e "${YELLOW}   - Device is unlocked${NC}"
        echo -e "${YELLOW}   - USB connection mode is set to 'File Transfer' or 'PTP'${NC}"
        return 1
    fi
}

# Function to check for port conflicts
check_port_conflicts() {
    echo -e "${BLUE}🔍 Checking for Port Conflicts...${NC}"
    
    local adb_port_used=$(lsof -ti:5037 2>/dev/null | wc -l)
    local maestro_ports_used=$(lsof -ti:7001,7002,7003 2>/dev/null | wc -l)
    
    if [ "$adb_port_used" -gt 1 ]; then
        echo -e "${YELLOW}⚠️  Multiple processes using ADB port 5037${NC}"
        log_verbose "ADB port processes: $(lsof -ti:5037 2>/dev/null | tr '\n' ' ')"
    else
        echo -e "${GREEN}✅ ADB port 5037 looks good${NC}"
    fi
    
    if [ "$maestro_ports_used" -gt 0 ]; then
        echo -e "${YELLOW}⚠️  Maestro ports (7001-7003) in use${NC}"
        log_verbose "Maestro port processes: $(lsof -ti:7001,7002,7003 2>/dev/null | tr '\n' ' ')"
    else
        echo -e "${GREEN}✅ Maestro ports available${NC}"
    fi
}

# Function to check Lenskart app installation
check_app_installation() {
    echo -e "${BLUE}🔍 Checking Lenskart App Installation...${NC}"
    
    local app_installed=$(adb shell pm list packages | grep -c "com.lenskart.app" || echo "0")
    
    if [ "$app_installed" -gt 0 ]; then
        echo -e "${GREEN}✅ Lenskart app (com.lenskart.app) is installed${NC}"
        
        # Check if app can be launched
        log_verbose "Checking app launch capability..."
        local launch_check=$(adb shell monkey -p com.lenskart.app -c android.intent.category.LAUNCHER 1 2>/dev/null)
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ App can be launched${NC}"
        else
            echo -e "${YELLOW}⚠️  App launch test failed${NC}"
        fi
        return 0
    else
        echo -e "${RED}❌ Lenskart app not found${NC}"
        echo -e "${YELLOW}💡 Install the Lenskart app on your device${NC}"
        return 1
    fi
}

# Function to test basic Maestro functionality
test_maestro_basic() {
    echo -e "${BLUE}🔍 Testing Basic Maestro Functionality...${NC}"
    
    if ! command_exists maestro; then
        echo -e "${RED}❌ Maestro not available for testing${NC}"
        return 1
    fi
    
    # Test maestro studio command (dry run)
    log_verbose "Testing maestro studio availability..."
    if maestro studio --help >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Maestro studio command available${NC}"
    else
        echo -e "${YELLOW}⚠️  Maestro studio command issues${NC}"
    fi
    
    # Test device detection by maestro
    log_verbose "Testing maestro device detection..."
    local maestro_devices=$(maestro test --help 2>/dev/null | grep -c "device" || echo "0")
    if [ "$maestro_devices" -gt 0 ]; then
        echo -e "${GREEN}✅ Maestro can access device commands${NC}"
    else
        echo -e "${YELLOW}⚠️  Maestro device access unclear${NC}"
    fi
}

# Function to apply fixes
apply_fixes() {
    if [ "$CHECK_ONLY" = true ]; then
        echo -e "${YELLOW}🔍 Skipping fixes (check-only mode)${NC}"
        return 0
    fi
    
    echo -e "${BLUE}🛠️  Applying Fixes...${NC}"
    
    # Always restart ADB if force restart or if there were device issues
    if [ "$FORCE_RESTART" = true ] || ! check_device_connectivity >/dev/null 2>&1; then
        restart_adb_server
    fi
    
    # Kill any hanging Maestro processes
    echo -e "${BLUE}🔄 Cleaning up Maestro processes...${NC}"
    pkill -f maestro >/dev/null 2>&1 || true
    sleep 2
    
    # Clear any temporary Maestro files
    if [ -d "/tmp/maestro" ]; then
        log_verbose "Cleaning Maestro temp files..."
        rm -rf /tmp/maestro/* 2>/dev/null || true
    fi
    
    echo -e "${GREEN}✅ Fixes applied${NC}"
}

# Function to run a quick test
run_quick_test() {
    if [ "$CHECK_ONLY" = true ]; then
        echo -e "${YELLOW}🔍 Skipping test (check-only mode)${NC}"
        return 0
    fi
    
    echo -e "${BLUE}🧪 Running Quick Connection Test...${NC}"
    
    # Check if we have a simple test file
    local test_file=""
    if [ -f "login/Login.yaml" ]; then
        test_file="login/Login.yaml"
    elif [ -f "vpn/disconnect-vpn.yaml" ]; then
        test_file="vpn/disconnect-vpn.yaml"
    fi
    
    if [ -n "$test_file" ]; then
        echo -e "${YELLOW}   📄 Testing with: $test_file${NC}"
        echo -e "${YELLOW}   ⏳ Running dry-run test...${NC}"
        
        # Run a dry-run test to verify connectivity
        if maestro test "$test_file" --dry-run >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Quick test passed - Maestro can connect to device${NC}"
            return 0
        else
            echo -e "${RED}❌ Quick test failed - connection issues persist${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  No suitable test file found for quick test${NC}"
        return 0
    fi
}

# Main execution
echo -e "${BLUE}🚀 Starting Diagnostics...${NC}"
echo ""

# Track overall status
overall_status=0

# Run all checks
check_adb || overall_status=1
echo ""

check_maestro || overall_status=1
echo ""

check_device_connectivity || overall_status=1
echo ""

check_port_conflicts
echo ""

check_app_installation || overall_status=1
echo ""

test_maestro_basic
echo ""

# Apply fixes if needed and not in check-only mode
if [ $overall_status -ne 0 ] || [ "$FORCE_RESTART" = true ]; then
    apply_fixes
    echo ""
    
    # Re-check device connectivity after fixes
    echo -e "${BLUE}🔍 Re-checking Device Connectivity After Fixes...${NC}"
    if check_device_connectivity; then
        overall_status=0
    fi
    echo ""
fi

# Run quick test
run_quick_test
echo ""

# Final summary
echo -e "${BLUE}📊 SUMMARY${NC}"
echo -e "${BLUE}═══════════════════════════════════════${NC}"

if [ $overall_status -eq 0 ]; then
    echo -e "${GREEN}✅ All checks passed - Maestro should be ready to use${NC}"
    echo ""
    echo -e "${YELLOW}💡 Next steps:${NC}"
    echo -e "${YELLOW}   - Run: maestro test login/Login.yaml${NC}"
    echo -e "${YELLOW}   - Or: ./sanity.sh IN${NC}"
    echo -e "${YELLOW}   - Or: ./regression.sh${NC}"
else
    echo -e "${RED}❌ Some issues detected - manual intervention may be required${NC}"
    echo ""
    echo -e "${YELLOW}💡 Manual troubleshooting steps:${NC}"
    echo -e "${YELLOW}   1. Check USB cable and connection${NC}"
    echo -e "${YELLOW}   2. Enable USB Debugging in Developer Options${NC}"
    echo -e "${YELLOW}   3. Accept USB debugging authorization on device${NC}"
    echo -e "${YELLOW}   4. Try different USB port or cable${NC}"
    echo -e "${YELLOW}   5. Restart device and computer${NC}"
    echo -e "${YELLOW}   6. Check device manufacturer USB drivers${NC}"
fi

echo ""
echo -e "${BLUE}🔧 Script completed at $(date '+%H:%M:%S')${NC}"

exit $overall_status
