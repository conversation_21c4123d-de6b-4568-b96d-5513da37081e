# Lenskart App Automation Framework

A comprehensive mobile app automation framework for functional testing of Lenskart's Android and iOS applications using Maestro. This framework provides end-to-end test automation for critical user journeys including product browsing, cart management, order placement, and user account operations.

## 🎯 Overview

This framework leverages **Maestro** - a simple, powerful, and reliable UI testing framework for mobile apps. It enables automated testing of complex user flows across the Lenskart mobile application ecosystem.

### Key Features

- ✅ **Cross-Platform Support**: Android and iOS app testing
- ✅ **Modular Test Architecture**: Reusable components and flows
- ✅ **End-to-End Test Suites**: Complete order placement journeys
- ✅ **Environment Configuration**: Flexible test data management
- ✅ **Parallel Execution**: Multiple test flows with reporting
- ✅ **Visual Validation**: UI element assertions and interactions
- ✅ **Real Device Testing**: Support for physical devices and emulators

### Supported Test Scenarios

- **Product Discovery**: Search, filtering, sorting, and product browsing
- **User Authentication**: Login, OTP verification, profile management
- **Shopping Cart**: Add/remove items, apply coupons, manage addresses
- **Order Placement**: Complete purchase flows for eyeglasses, sunglasses, and contact lenses
- **Power Configuration**: Prescription management and lens customization
- **Home Try-On (HTO)**: Appointment booking and location services
- **Payment Processing**: Multiple payment methods and retry scenarios

## 🏗️ Framework Architecture

```
app-automation/
├── config.yaml                 # Main test suite configuration
├── Sanity.yaml                # Sanity test configuration
├── run_all_flows.sh           # Batch execution script
├── run_suite.sh               # Suite execution script
├── reports/                   # Test execution reports
├── e2e/                       # End-to-end test suites
│   ├── eyeglass/             # Eyeglass order flows
│   ├── sunglass/             # Sunglass order flows
│   ├── cl/                   # Contact lens flows
│   └── hto/                  # Home try-on flows
├── login/                     # Authentication flows
├── search/                    # Product search flows
├── cart/                      # Shopping cart operations
├── payment/                   # Payment processing
├── address/                   # Address management
├── power/                     # Prescription & lens configuration
├── pdp/                       # Product detail page tests
├── plp/                       # Product listing page tests
├── profile/                   # User profile management
└── [feature-modules]/         # Additional feature-specific tests
```

## 🚀 Getting Started

### Prerequisites

1. **Maestro Installation**
   ```bash
   # Install Maestro CLI
   curl -Ls "https://get.maestro.dev" | bash

   # Verify installation
   maestro --version
   ```

2. **Mobile Device Setup**
   - **Android**: Enable Developer Options and USB Debugging
   - **iOS**: Enable Developer Mode and trust the development certificate
   - **Emulator/Simulator**: Ensure device is running and accessible via ADB/Xcode

3. **Lenskart App Installation**
   - Install the Lenskart app on your test device
   - App ID: `com.lenskart.app`

### Setup Instructions

1. **Clone the Repository**
   ```bash
   git clone https://gitlab.lenskart.com/amitagarwal/app-automation.git
   cd app-automation
   ```

2. **Verify Device Connection**
   ```bash
   # For Android
   adb devices

   # For iOS
   xcrun simctl list devices
   ```

3. **Run a Sample Test**
   ```bash
   # Execute a simple login flow
   maestro test login/Login.yaml

   # Run sanity tests
   maestro test Sanity.yaml
   ```

## 📱 Framework Components

### Core Configuration Files

#### `config.yaml`
Main configuration file defining the test suite structure:
```yaml
appId: com.lenskart.app
name: Lenskart Regression Suite
continueOnFailure: true
flows:
  - Eyeglass/
  - Test3/Sunglasses/*
```

#### `Sanity.yaml`
Quick smoke tests for basic functionality validation.

### Test Module Categories

#### 🔐 Authentication (`login/`)
- **Login.yaml**: Standard mobile number + OTP login
- **loginBottomsheetFromDifferentFeatures.yaml**: Context-specific login flows
- **ReSendOtp.yaml**: OTP resend functionality
- **Popup.yaml**: Login popup handling

#### 🔍 Search & Discovery (`search/`, `plp/`, `pdp/`)
- **SearchKeyword.yaml**: Keyword-based product search
- **Filter.yaml**: Product filtering options
- **Sort.yaml**: Product sorting mechanisms
- **PDP_Validation.yaml**: Product detail page validations
- **GalleryView.yaml**: Product image gallery interactions

#### 🛒 Shopping Cart (`cart/`)
- **gotoCart.yaml**: Cart navigation
- **productCards/**: Product management in cart
- **offers/**: Coupon and discount applications
- **billDetails/**: Price breakdown and calculations
- **insurance/**: Insurance option handling

#### 💳 Payment (`payment/`)
- **CreditCardDetails.yaml**: Credit card payment flow
- **PaymentRetry.yaml**: Payment failure and retry scenarios

#### 📍 Address Management (`address/`)
- **AddNewAddress.yaml**: Manual address entry
- **AddNewAddressCurrentLocation.yaml**: GPS-based address
- **SavedAddress.yaml**: Saved address selection
- **EditSavedAddress.yaml**: Address modification

#### 👓 Power & Lens Configuration (`power/`, `addPower/`)
- **WithPowerEyeglass*.yaml**: Prescription eyeglass configurations
- **ZeroPower*.yaml**: Non-prescription lens options
- **AddPowerEnterManually.yaml**: Manual prescription entry
- **AddPowerUploadPrescription.yaml**: Prescription upload flow

### End-to-End Test Suites (`e2e/`)

#### Eyeglass Flows (`e2e/eyeglass/`)
- **EyeglassFrameOnlyOrderplacement.yaml**: Frame-only purchase
- **EyeglassWithPowerOrderPlacement.yaml**: Prescription eyeglass order
- **EglassWithPowerAntiGlarePremiumOrderPlacement.yaml**: Premium lens order

#### Sunglass Flows (`e2e/sunglass/`)
- **SunglassWithPowerOrderPlacement.yaml**: Prescription sunglass order
- **SunglasseswithoutPowerOrderplacement.yaml**: Non-prescription sunglass order

#### Contact Lens Flows (`e2e/cl/`)
- **ContactLensOrderPlacement.yaml**: Contact lens purchase flow

#### Home Try-On Flows (`e2e/hto/`)
- **HTOBookAppointment.yaml**: Appointment booking flow

## 🎮 Test Execution

### Running Individual Tests

```bash
# Execute a specific test flow
maestro test login/Login.yaml

# Run with custom environment variables
maestro test search/SearchKeyword.yaml --env SEARCHKEYWORD="Ray-Ban"

# Run with output reporting
maestro test e2e/eyeglass/eyeglass-frame-only.yaml --output reports/eyeglass_test
```

### Running Test Suites

#### Using Configuration Files
```bash
# Run the main regression suite
maestro test config.yaml

# Run sanity tests
maestro test Sanity.yaml
```

#### Using Execution Scripts
```bash
# Run all critical flows
./run_all_flows.sh

# Run the complete test suite
./run_suite.sh
```

### Batch Execution with Reporting

```bash
# Execute multiple flows with timestamped reports
maestro test e2e/eyeglass/eyeglass-frame-only.yaml --output reports/eyeglass_$(date +%Y%m%d_%H%M%S)
maestro test e2e/sunglass/SunglassWithPowerOrderPlacement.yaml --output reports/sunglass_$(date +%Y%m%d_%H%M%S)
```

### Parallel Execution

```bash
# Run multiple test suites in parallel
maestro test config.yaml --device-id device1 &
maestro test Sanity.yaml --device-id device2 &
wait
```

## ⚙️ Configuration Management

### Environment Variables

Tests support dynamic configuration through environment variables:

```
# Example: login/Login.yaml
env:
  MOBILENO: 2123000009
  OTP: 7777

# Usage in test steps
- inputText: ${MOBILENO}
- inputText: ${OTP}
```

### Common Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `MOBILENO` | Test mobile number | `2123000009` |
| `OTP` | Test OTP code | `7777` |
| `SEARCHKEYWORD` | Product search term | `"Ray-Ban sunglasses"` |
| `USERID` | Test user identifier | `testuser123` |

### Device Configuration

```yaml
# config.yaml structure
appId: com.lenskart.app
name: Lenskart Regression Suite
continueOnFailure: true
flows:
  - path/to/test/flows
```

### Flow Composition

Tests can be composed using `runFlow` commands:

```yaml
# Reusable flow execution
- runFlow:
    file: ../../search/SearchKeyword.yaml
    env:
      SEARCHKEYWORD: "Frame only Eyeglass"
      label: "Search keyword passed as Frame only Eyeglass"
```

## 📝 Writing New Tests

### Test Structure

Every test file follows this structure:

```yaml
appId: com.lenskart.app

env:
  VARIABLE_NAME: value

---
# Test steps
- clearState
- launchApp
- tapOn: "element"
- inputText: "text"
- assertVisible: "expected element"
```

### Best Practices

#### 1. **Modular Design**
- Create reusable flows for common operations
- Use `runFlow` to compose complex scenarios
- Separate concerns (login, navigation, actions)

#### 2. **Element Identification**
```yaml
# Prefer ID-based selectors
- tapOn:
    id: "com.lenskart.app:id/btn_login"

# Use text selectors for dynamic content
- tapOn: "Add to Cart"

# Use index for multiple similar elements
- tapOn:
    id: "com.lenskart.app:id/product_card"
    index: 0
```

#### 3. **Error Handling**
```
# Optional interactions
- tapOn:
    text: "Skip"
    optional: true

# Conditional flows
- runFlow:
    when:
      visible: 'Go to Home'
    commands:
      - tapOn: 'Go to Home'
```

#### 4. **Environment Variables**
```
# Define at the top of the file
env:
  SEARCH_TERM: "eyeglasses"
  USER_MOBILE: "9876543210"

# Use throughout the test
- inputText: ${SEARCH_TERM}
```

### Test Categories

#### Unit Tests
- Single feature validation
- Isolated component testing
- Quick feedback loops

#### Integration Tests
- Multi-component interactions
- Feature-to-feature workflows
- Cross-module dependencies

#### End-to-End Tests
- Complete user journeys
- Business-critical paths
- Full application workflows

## 📊 Reporting & Analysis

### Test Reports

Reports are generated in the `reports/` directory with the following structure:

```
reports/
├── flow1_20241230_143022/
│   ├── report.html
│   ├── screenshots/
│   └── logs/
└── flow2_20241230_143155/
    ├── report.html
    ├── screenshots/
    └── logs/
```

### Report Contents

- **HTML Report**: Visual test execution summary
- **Screenshots**: Step-by-step visual evidence
- **Logs**: Detailed execution traces
- **Performance Metrics**: Execution timing data

### Analyzing Failures

1. **Check Screenshots**: Visual evidence of failure points
2. **Review Logs**: Detailed error messages and stack traces
3. **Verify Selectors**: Ensure UI elements are correctly identified
4. **Environment Issues**: Check device state and app version

## 🔧 Troubleshooting

### Common Issues

#### 1. **Element Not Found**
```yaml
# Solution: Add wait conditions
- waitForAnimationToEnd
- assertVisible: "expected element"
- tapOn: "expected element"
```

#### 2. **App State Issues**
```yaml
# Solution: Reset app state
- clearState
- launchApp
```

#### 3. **Timing Issues**
```yaml
# Solution: Add explicit waits
- waitForAnimationToEnd
- wait: 2000  # Wait 2 seconds
```

#### 4. **Device Connection**
```bash
# Check device connectivity
adb devices
maestro test --help
```

### Debug Mode

```bash
# Run tests with verbose logging
maestro test login/Login.yaml --debug

# Run with Maestro Studio for interactive debugging
maestro studio
```

### Performance Optimization

- Use specific selectors (ID > text > xpath)
- Minimize wait times
- Leverage parallel execution
- Clean up app state between tests

## 🤝 Contributing Guidelines

### Code Standards

#### File Naming Convention
- Use descriptive, kebab-case names: `eyeglass-with-power-order.yaml`
- Include feature prefix: `login-with-otp.yaml`
- Use consistent suffixes: `*OrderPlacement.yaml`, `*Validation.yaml`

#### YAML Structure
```yaml
# Required header
appId: com.lenskart.app

# Environment variables (if needed)
env:
  VARIABLE_NAME: value

# Separator
---

# Test steps with comments
# Step 1: Clear app state
- clearState

# Step 2: Launch application
- launchApp

# Step 3: Perform actions
- tapOn: "element"
```

#### Documentation Requirements
- Add comments for complex interactions
- Document environment variables
- Explain conditional logic
- Include expected outcomes

### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/new-test-flow
   ```

2. **Follow Naming Conventions**
   - Branch: `feature/description` or `bugfix/issue-number`
   - Commits: Clear, descriptive messages

3. **Test Your Changes**
   ```bash
   # Validate new tests
   maestro test your-new-test.yaml

   # Run regression suite
   ./run_suite.sh
   ```

4. **Update Documentation**
   - Add new tests to this README
   - Document any new environment variables
   - Update configuration files if needed

### Review Checklist

- [ ] Test executes successfully
- [ ] Follows naming conventions
- [ ] Includes proper error handling
- [ ] Uses environment variables appropriately
- [ ] Has adequate documentation
- [ ] Doesn't break existing tests

## 📚 Advanced Usage

### Custom Test Suites

Create custom configuration files for specific test scenarios:

```yaml
# custom-suite.yaml
appId: com.lenskart.app
name: Custom Test Suite
continueOnFailure: false
flows:
  - e2e/eyeglass/eyeglass-frame-only.yaml
  - e2e/sunglass/SunglassWithPowerOrderPlacement.yaml
```

### Data-Driven Testing

Use environment variables for data-driven scenarios:

```bash
# Run same test with different data
maestro test search/SearchKeyword.yaml --env SEARCHKEYWORD="Ray-Ban"
maestro test search/SearchKeyword.yaml --env SEARCHKEYWORD="Oakley"
maestro test search/SearchKeyword.yaml --env SEARCHKEYWORD="Lenskart"
```

### Cross-Platform Testing

```bash
# Android testing
maestro test --platform android config.yaml

# iOS testing
maestro test --platform ios config.yaml
```

### Continuous Integration

#### GitHub Actions Example
```yaml
name: Mobile App Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Maestro
        run: |
          curl -Ls "https://get.maestro.dev" | bash
          echo "$HOME/.maestro/bin" >> $GITHUB_PATH
      - name: Run Tests
        run: maestro test config.yaml
```

## 📖 References & Resources

## To Send mail pass the flag

```yaml
./sanity.sh IN sendmail=true
```


```yaml
./sanity.sh SG sendmail=true
```


```yaml
./sanity.sh UAE sendmail=true
```


```yaml
./sanity.sh SA sendmail=true
```

```yaml
./sanity.sh TH sendmail=true
```


## To not trigger email

```yaml
./sanity.sh IN
```



### Official Documentation
- **Maestro Documentation**: https://docs.maestro.dev/
- **API Reference**: https://docs.maestro.dev/api-reference/commands
- **Maestro Studio**: https://docs.maestro.dev/getting-started/maestro-studio

### Useful Commands
```bash
# Interactive test development
maestro studio

# Record user interactions
maestro record

# Test on cloud devices
maestro cloud

# Validate YAML syntax
maestro test --dry-run test-file.yaml
```

### Community Resources
- **GitHub Repository**: https://github.com/mobile-dev-inc/maestro
- **Discord Community**: https://discord.gg/JS2ByJAP
- **Stack Overflow**: Tag `maestro-testing`

## 🏷️ Version History

| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 2024-12-30 | Initial framework setup with core flows |
| 1.1.0 | TBD | Enhanced reporting and parallel execution |
| 1.2.0 | TBD | Cross-platform optimization |

## 📄 License

This project is proprietary to Lenskart and is intended for internal testing purposes only.

## 👥 Team & Support
Shivani Goudar
Anuja
Mahadev
Kanika
Mahathi
Rahul
Niharika
Nidhi

### Framework Maintainers
- **Primary Contact**: QE Team (<EMAIL>)

### Getting Help
1. **Internal Documentation**: Check this README and inline comments
2. **Team Slack**: #app-automation-support
3. **Issue Tracking**: GitLab Issues
4. **Knowledge Base**: Confluence QA Space

---

**Happy Testing! 🚀**

*This framework is designed to ensure the highest quality of the Lenskart mobile application through comprehensive automated testing.*

