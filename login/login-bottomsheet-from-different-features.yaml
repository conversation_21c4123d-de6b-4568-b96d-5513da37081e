appId: "com.lenskart.app"

env:
  MOBILENO: 8888888888
  OTP: 7777

---

#check if login-signup bottomsheet is popped up when user is in guest state and tries tapOn:
  # apply coupon in cart
   # apply insurance from cart
   # proceed to payment from cart

- assertVisible: "Login or signup"

# Enter mobil no text field
- tapOn:
    id: "com.lenskart.app:id/tiet_text_input"
- inputText: ${MOBILENO}

# OTP field
- tapOn:
    id: "com.lenskart.app:id/btn_get_otp"

- tapOn:
    id: "com.lenskart.app:id/et_otp"

- inputText: ${OTP}