appId: com.lenskart.app
---

- runFlow: login.yaml

# tap on Profile icon
- tapOn:
    id: "com.lenskart.app:id/iv_initials_holder"
    label: tap on Profile icon

# Verify user in on My Profile screen
- assertVisible: "My Profile"

# Scroll down
- scrollUntilVisible:
    element:
      text: "My addresses" # or any other selector
    direction: DOWN # DOWN|UP|LEFT|RIGHT (optional, default: DOWN)
    timeout: 50000 # (optional, default: 20000) ms
    speed: 40 # 0-100 (optional, default: 40) Scroll speed. Higher values scroll faster.
    visibilityPercentage: 100 # 0-100 (optional, default: 100) Percentage of element visible in viewport
    centerElement: false # true|false (optional, default: false)

- tapOn: "My addresses"

- runFlow: add-new-address.yaml

