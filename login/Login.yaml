appId: com.lenskart.app

env:
  MOBILENO: 1000022235
  OTP: 7777
---
- clearState

- launchApp

- assertVisible: "Login or signup"


- tapOn:
    id: "com.lenskart.app:id/tiet_text_input"

- inputText: ${MOBILENO}

- tapOn:
    id: "com.lenskart.app:id/btn_get_otp"

- tapOn:
    id: "com.lenskart.app:id/et_otp"

- inputText: ${OTP}

# Verify if "Go to Home" screen is displayed. This is a configurable screen
- runFlow:
    when:
      visible: 'Go to Home'
    commands:
      - tapOn: 'Go to Home'



