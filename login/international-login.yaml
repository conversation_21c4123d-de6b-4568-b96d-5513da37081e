appId: com.lenskart.app

env:
  MOBILENO: 2123000009     # Default fallback
  OTP: 7777
  COUNTRY: India           # Default fallback
---

# Clear app state and launch app

- launchApp

# Wait for app to load completely
- waitForAnimationToEnd

# Select country
- tapOn:
    id: "com.lenskart.app:id/iv_dropdown"
    optional: true

- tapOn:
    id: "com.lenskart.app:id/search_bar"
    optional: true

- tapOn:
    id: "com.lenskart.app:id/textinput_placeholder"
    optional: true


- inputText: ${COUNTRY}

- tapOn:
    text: "${COUNTRY}"
    index: 1

# Enter mobile number
- tapOn:
    id: "com.lenskart.app:id/tiet_text_input"
    optional: true

- tapOn:
    id: "com.lenskart.app:id/textinput_placeholder"
    optional: true


- inputText: ${MOBILENO}

# Tap Get OTP
- tapOn:
    id: "com.lenskart.app:id/btn_get_otp"
    optional: true

# Enter OTP
- inputText: ${OTP}

- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

# Handle permission popups
- tapOn:
    text: "Allow"
    optional: true

- tapOn:
    text: "While using the app"
    optional: true

# Final wait to ensure app is ready
- waitForAnimationToEnd


