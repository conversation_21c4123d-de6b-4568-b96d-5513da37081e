appId: com.lenskart.app
---

- launchApp

# Wait for app to fully load
- waitForAnimationToEnd

# Handle login with better error handling
- tapOn:
    text: "Enter 10-digit phone number"
    optional: true

- inputText: "5000000000"

- tapOn:
    text: "Get OTP"
    optional: true

# Wait for OTP screen
- waitForAnimationToEnd

- inputText: "77 77"

- tapOn:
    text: "Go to Home"
    optional: true

# Wait for home screen to load

- runFlow:
    when: 
      visible: 'Allow'
    commands:
        - tapOn: "Allow"
- runFlow:
    when: 
      visible: 'While using the app'
    commands:
        - tapOn: "While using the app"


