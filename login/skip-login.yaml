appId: com.lenskart.app
---

- launchApp

# skip login
- tapOn:
    #id: "com.lenskart.com:id/btn_skip"
    text: "Skip"

# Tap on Go to home
- tapOn:
    #id: "com.lenskart.com:id/btnCta"
    text: "Go to Home"

# Allow permission from popup
- runFlow:
    when:
      visible: 'Allow'
    commands:
      - tapOn: "Allow"

# location permission popup
- runFlow:
    when:
      visible: 'While using the app'
    commands:
      - tapOn: "While using the app"