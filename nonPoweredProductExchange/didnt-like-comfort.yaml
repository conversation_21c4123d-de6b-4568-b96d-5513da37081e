appId: com.lenskart.app

---

- runFlow: ../../login/Popup.yaml

- tapOn:
      id: "com.lenskart.app:id/iv_initials_holder"

- tapOn:
      id: "com.lenskart.app:id/image"
      index: 0

- scrollUntilVisible:
      element:
          text: "Exchange"
      visibilityPercentage: 80
      speed: 60

- tapOn: "Exchange"


- runFlow : ../../returnExchangeReasons/didnt-like-comfort-reason.yaml

- runFlow : ../../returnExchangeReasons/exchange-flow.yaml

- tapOn: "Select All"

- tapOn: "Continue"

- tapOn: "Select All"

- tapOn: "Continue"

- tapOn: "Select All"

- tapOn: "Continue"

- tapOn:
      text: "Exchange for FREE"
      index: 0

- tapOn: "Place Order"