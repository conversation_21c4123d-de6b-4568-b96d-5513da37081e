appId: "com.lenskart.app"
env:
  LAST_USED_PAYMENY_METHOD: "com.lenskart.app:id/tv_payment_type"
---

# check if express payment widget is shown
- assertVisible:
    id: "com.lenskart.app:id/bottom_bar_express"

# check for last used payment method
- assertVisible:
    # text: "PAY USING"
    id: ${LAST_USED_PAYMENY_METHOD}

#tapping on payment method will take to payments screen
- tapOn:
   id: ${LAST_USED_PAYMENY_METHOD}

- back

# direct proceed with last used payment method by tapping on pay now button
- tapOn:
    id: "com.lenskart.app:id/btn_pay_full_continue"


# check for cancel payment botoomsheet
#- assertVisible:
 #   id: "com.lenskart.app:id/bottom_progress"
- tapOn:
    #id: "com.lenskart.app:id/cancel_payment"
    text: "Cancel Payment"
