appId : "com.lenskart.app"
---

# check if express delivery address widget is showing in cart
- runFlow:
    when:
      visible:
        id: "com.lenskart.app:id/layout_express_address"
    commands:
      - tapOn: "Change"


# check if saved address is present in change address bottomsheet
- assertVisible:
    id: "com.lenskart.app:id/tv_address_heading"


# select any saved address
- scrollUntilVisible:
    element:
      id: "com.lenskart.app:id/main_container"
      index: 2
    direction: DOWN
    visibilityPercentage: 100
    centerElement: true
- tapOn:
    id: "com.lenskart.app:id/proceed_icon"


# edit any saved address
- tapOn:
   text: "Change"
- tapOn:
    text: "Edit"
    index: 1
- tapOn: "Enter complete address"
- tapOn: "Flat no / House no / Building*"
- inputText: "Test Order"
- tapOn: "Area / Street / Sector / Village*"
- inputText: "Abc"
- tapOn: "Save address & Proceed"

#bak to cart
- back

# delete saved address
- tapOn:
    id: "com.lenskart.app:id/tv_delete"
- tapOn:
    id: "com.lenskart.app:id/btn_primary"

# come back to cart
- back

# add a new address
- tapOn: "Change"
- tapOn: "Add address manually"
- tapOn: "Pincode*"
- eraseText: 6
- inputText: "201014"
- tapOn: "House no, Building name*"
- inputText: "my house"
- tapOn: "Road name, Area, Locality*"
- tapOn: "Save address & Proceed"

# come back to cart
- back


