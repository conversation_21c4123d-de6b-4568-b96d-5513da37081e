appId: com.lenskart.app

---

- scrollUntilVisible:
    element:
      text: "Nearby stores"
    direction: DOWN
    speed: 30
    timeout: 30000
    visibilityPercentage: 50
    waitToSettleTimeoutMs: 500
- tapOn: "Find stores nearby"
- runFlow:
    when:
      visible: "NOT NOW"
    commands:
      - tapOn: "NOT NOW"
- runFlow:
    when:
      visible: "CANCEL"
    commands:
      - tapOn: "CANCEL"
- tapOn: "Book Appointment"
- tapOn:
    id: "com.lenskart.app:id/tv_time"
    index: 2
- tapOn:
    id: "com.lenskart.app:id/tv_day"
    index: 3
- scrollUntilVisible:
    element:
      text: "Pick a time"
    direction: DOWN
    speed: 80
    timeout: 10000
    visibilityPercentage: 100
    centerElement: true
- tapOn: "15:30 - 16:00"
- tapOn: "Confirm & Book"
- tapOn:
    id: "com.lenskart.app:id/appointmentDetailsLayout"
    index: 1
- tapOn:
    id: "com.lenskart.app:id/btn_continue"
