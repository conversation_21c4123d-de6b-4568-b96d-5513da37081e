appId: com.lenskart.app

env:
  SEARCHKEYWORD: "Contact Lenses"

---
# Step 1: Login to the app
- runFlow: ../../../login/popup.yaml

- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: "aquacolor candypack Contact Lens-cylindrical"

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

- waitForAnimationToEnd

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/cl-new-saved-power.yaml

# Step 7: Handle skip to cart option
- runFlow:
    when:
      visible: 'Skip to cart'
    commands:
      - tapOn: "Skip to cart"

- waitForAnimationToEnd

#- runFlow: ../../../address/home-delivery.yaml
- runFlow: ../../../address/saved-address.yaml

- runFlow: ../../../payment/credit-card-details.yaml



