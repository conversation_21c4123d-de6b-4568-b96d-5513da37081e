appId: com.lenskart.app

---
- runFlow: ../../../login/popup.yaml

- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: aquacolor candypack Contact Lens-cylindrical # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as aquacolor candypack Contact Lens-cylindrical
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

- waitForAnimationToEnd

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/c-l-enter-power-manually.yaml

- runFlow:
    when:
      visible: 'Skip to cart'
    commands:
      - tapOn: "Skip to cart"

#- runFlow: ../../../address/home-delivery.yaml

- runFlow: ../../../address/saved-address.yaml

- runFlow: ../../../payment/credit-card-details.yaml

