appId: com.lenskart.app

---

- runFlow: ../../../login/popup.yaml

- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : sunglasses for men # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as sunglasses for men

#It will select the 1st product from Sunglasses PLP

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1


- tapOn: "Add to Cart"

- waitForAnimationToEnd

- runFlow:
    file: ../../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Brown Tinted Power Lenses"

- runFlow: ../../../addPower/add-power-submit-later.yaml

- waitForAnimationToEnd

#- runFlow: ../../../address/saved-address.yaml

- runFlow: ../../../payment/credit-card-details.yaml
