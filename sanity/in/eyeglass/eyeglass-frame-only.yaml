appId: com.lenskart.app

---
- runFlow: ../../../login/popup.yaml

- runFlow:
     file: ../../../search/search-keyword.yaml
     env:
      SEARCHKEYWORD: Frame only Eyeglass # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Frame only Eyeglass
- pressKey: Enter
- tapOn:
        id: "com.lenskart.app:id/image"
        index: 1
- tapOn: "Add to Cart"

# Wait for cart options to load
- waitForAnimationToEnd

# Select Frame only option (without power)
- tapOn:
    text: "Frame Only"
    optional: true

# Wait for frame only selection to process
- waitForAnimationToEnd

# Proceed to cart/checkout
- tapOn:
    text: "Proceed to Checkout"
    optional: true

# Alternative: Look for "Go to Cart" or "View Cart"
- tapOn:
    text: "Go to Cart"
    optional: true

- waitForAnimationToEnd

# Use address flow
#- runFlow: ../../../address/home-delivery.yaml
- runFlow:
    when:
      visible: 'Select Address'
    commands:
      - tapOn: "Select Address"

- runFlow: ../../../address/saved-address.yaml
# Now run payment flow
- runFlow: ../../../payment/credit-card-details.yaml







