appId: com.lenskart.app

---
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Singapore

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: Women sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Sunglasses

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
    optional: true

- tapOn:
    id: "com.lenskart.app:id/trailing_icon"
    index: 0
    optional: true
#select lens from PLP
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
    optional: true


- runFlow: ../../../pdp/sg-package-screen/sg-Sunglass-with-power.yaml

- runFlow:
    file: ../../../power/optimized-eyeglass-with-power.yaml
    env:
      lensType: "Blue Tinted"

- runFlow: ../../../addPower/international-add-power-saved-powers.yaml

- runFlow: ../../../cart/sg-cart/sg-regular-product-cart.yaml

- runFlow: ../../../address/sg-saved-address.yaml

- scrollUntilVisible:
    element:
      text: "Atome"
    direction: DOWN

- runFlow: ../../../payment/sg-atome-payment.yaml

