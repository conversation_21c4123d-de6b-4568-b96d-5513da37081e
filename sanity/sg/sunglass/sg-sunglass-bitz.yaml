appId: com.lenskart.app

---
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Singapore

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: 224465 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as PID

#click on product
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
    optional: true

- tapOn:
    id: "com.lenskart.app:id/rvSearch"
    optional: true


- runFlow: ../../../BitzFlow/with-button-select-bitz.yaml

- runFlow: ../../../pdp/sg-package-screen/sg-sunglass-without-power.yaml

- runFlow: ../../../address/saved-address.yaml

- scrollUntilVisible:
    element:
      text: "Atome"
    direction: DOWN

- runFlow: ../../../payment/sg-atome-payment.yaml