appId: com.lenskart.app

env:
  MOBILENO: 2123000009
  OTP: 7777
  SEARCHKEYWORD: "Eyeglass"

---

# Step 1: Login to the app with Singapore country
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Singapore

- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#Search keyword
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: 147411 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as PID

# Wait for search results
- waitForAnimationToEnd

#It will handel the PLP
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
    optional: true

- tapOn:
    id: "com.lenskart.app:id/rvSearch"
    optional: true


#add to cart
- tapOn:
    id: "com.lenskart.app:id/btn_add_to_cart"

# Wait for search results
- waitForAnimationToEnd

#select frame only
- tapOn: "Frame Only"

#  Use saved address
- runFlow: ../../../address/saved-address.yaml

- tapOn: "Credit/Debit Card"

# Process payment
- runFlow: ../../../payment/international-c-c-details.yaml






