appId: com.lenskart.app

---
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Singapore

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#verifying eyeglass section on home
- assertVisible:
    id: "com.lenskart.app:id/text_heading"
    index: 0

#clicking on progressive image on home
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 5

#cselect first eyeglass n plp
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- runFlow: ../../../pdp/sg-package-screen/sg-eyeglass-progressive.yaml

- runFlow:
    file: ../../../progressive/optimized-progressive.yaml
    env:
      lensType: "Tokai Progressive"

- tapOn: "Skip coating addition"

- runFlow: ../../../cart/sg-cart/sg-progressive-product-cart.yaml

- runFlow: ../../../address/sg-studioflow.yaml

- scrollUntilVisible:
      element:
          text: "GrabPay"
      direction: DOWN

- runFlow: ../../../payment/sg-grabpay-payment.yaml
