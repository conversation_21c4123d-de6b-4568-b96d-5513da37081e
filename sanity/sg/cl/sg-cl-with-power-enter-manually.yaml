appId: com.lenskart.app

---
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Singapore


- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- tapOn:
    text: "Go to home"
    optional: true


- runFlow:
      file: ../../../search/international-search-keyword.yaml
      env:
          SEARCHKEYWORD : 224271 # <-- Run commands from "searchkeyword.yaml"
          label: Search keywrod passed as 224271

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
    optional: true

- tapOn:
    id: "com.lenskart.app:id/rvSearch"
    optional: true

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/international-c-l-enter-power-manually.yaml

- runFlow:
      when:
          visible: "Add Contact Lens Solution"
      file: ../../../pdp/add-cl-solution.yaml

- runFlow: ../../../address/saved-address.yaml

- scrollUntilVisible:
    element:
      text: "ShopBack"
    direction: DOWN

- runFlow: ../../../payment/sg-shopback-payment.yaml
