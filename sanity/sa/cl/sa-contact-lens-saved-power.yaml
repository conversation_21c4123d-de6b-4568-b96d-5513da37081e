appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd

- tapOn:
      text: "Go to home"
      optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#click on contact lens tab

- tapOn: "Premium"
- tapOn: "Contact Lens"

#- scrollUntilVisible:
 #   element:
  #    text: "Contact Lenses"          # or use textMatches
 #   direction: DOWN
 #   speed: 60
  #  timeout: 20000
  #  visibilityPercentage: 50
  #  waitToSettleTimeoutMs: 500
 # 3  centerElement: true

#select cl
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#select cl category from bottomsheet
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
- runFlow: ../../../plp/sa-plp.yaml
- scrollUntilVisible:
    element:
      text:  "I will submit power later"
    direction: DOWN

#Tap on Submit Power Later
- tapOn:
    id: "com.lenskart.app:id/rb_ask_me_later"

#click on add to cart from pdp
- tapOn: "Add to Cart"

#select address
- tapOn:
    id: "com.lenskart.app:id/btn_continue"
- runFlow:
    when:
      visible: "Submit Eye Power"
    file: ../../../clPowerType/uae-cl-submit-power-later.yaml

- runFlow: ../../../address/uae-new-saved-address.yaml

- runFlow: ../../../payment/sa-tabby-payment.yaml
