
appId: com.lenskart.app

---
# Step 1: Login to the app with Singapore country
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Saudi Arabia

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#click on contact lens tab

- tapOn: "Premium"
- tapOn: "Contact Lens"
#- scrollUntilVisible:
 #   element:
  #    text: "Contact Lenses"          # or use textMatches
  #  direction: DOWN
   # speed: 60
  #  timeout: 20000
  #  visibilityPercentage: 50
  #  waitToSettleTimeoutMs: 500
  #  centerElement: true


#select cl
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#select cl category from bottomsheet
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
- runFlow: ../../../plp/sa-plp.yaml
- scrollUntilVisible:
    element:
      text:  "I will submit power later"
    direction: DOWN

#Tap on right spherical power
- tapOn: "Enter Power Manually"
- runFlow: ../../../clPowerType/international-c-l-enter-power-manually.yaml
#select address
- tapOn:
    id: "com.lenskart.app:id/btn_continue"
- runFlow:
     when:
       visible: "Submit Eye Power"
     file: ../../../clPowerType/uae-cl-submit-power-later.yaml
- tapOn:
    id: "com.lenskart.app.store:id/proceed_icon"
    index: 0
- tapOn: "Add Credit/ Debit/ ATM cards"
- runFlow: ../../../payment/international-c-c-details.yaml
