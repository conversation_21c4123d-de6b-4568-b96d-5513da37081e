appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
      text: "Go to home"
      optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow: ../../../home/<USER>
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- tapOn: "Add to Cart"

- tapOn: "Frame Only"
#select address
- tapOn:
    id: "com.lenskart.app:id/btn_continue"
- runFlow:
    when:
      visible: "Submit Eye Power"
    file: ../../../clPowerType/uae-cl-submit-power-later.yaml
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/sa-tamara-payment.yaml