appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow: ../../../home/<USER>
- tapOn:
      id: "com.lenskart.app:id/text_sort"
- tapOn: "Price: Low to High"

- runFlow: ../../../plp/sa-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- runFlow:
    file: ../../../power/optimizes-sunglasses-with-power.yaml
    env:
     lensType: "Brown Power Sun"
- runFlow: ../../../addPower/international-saved-powers.yaml
#select address
- tapOn:
    id: "com.lenskart.app:id/btn_continue"
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/sa-tabby-payment.yaml
