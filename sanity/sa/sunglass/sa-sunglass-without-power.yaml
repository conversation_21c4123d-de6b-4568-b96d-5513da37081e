appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
      when:
          visible: "For a better experience, your device will need to use Location Accuracy "
      file: ../../../home/<USER>

- runFlow: ../../../home/<USER>
- runFlow: ../../../plp/sa-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- tapOn: "Without Power"
#select address
- tapOn:
    id: "com.lenskart.app:id/btn_continue"
- runFlow:
    when:
      visible: "Submit Eye Power"
    file: ../../../clPowerType/uae-cl-submit-power-later.yaml
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/sa-tabby-payment.yaml