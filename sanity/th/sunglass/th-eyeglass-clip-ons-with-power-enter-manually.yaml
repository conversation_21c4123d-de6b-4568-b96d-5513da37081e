appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true
# Run login flow first
#- runFlow: ../../../login/international-login.yaml

#- runFlow: ../../../login/handle-permissions.yaml

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#Search anything using Search icon
- runFlow : ../../../search/international-search-icon.yaml

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: 150117 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as PID

#select product
- tapOn:
    id: "com.lenskart.app:id/tvProductId"

#add to cart
- tapOn: "Add to Cart"

#select package
- runFlow:
    file: ../../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Transition"
- tapOn: "Proceed to Cart"

#Select coatings
- runFlow:
    when:
      visible: "Add a Coating"
    file: ../../../power/international-coating.yaml

# add power by entering manually
- runFlow: ../../../addPower/add-power-enter-manully.yaml


#cart & address
- runFlow: ../../../address/th-saved-address.yaml

#payment
- runFlow: ../../../payment/th-promptpay-payment.yaml