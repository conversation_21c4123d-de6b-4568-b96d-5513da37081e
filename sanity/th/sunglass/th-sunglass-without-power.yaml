appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
   text: "Go to home"
   optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

# Run login flow first
#- runFlow: ../../../login/optimized-login.yaml

#- runFlow: ../../../login/handle-permissions.yaml

#Search keyword by using search icon
- runFlow: ../../../search/international-search-icon.yaml

#Search keyword
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: women sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as women sunglasses

#clicking on sunglasses text
- tapOn:
    id: "com.lenskart.app:id/trailing_icon"
    index: 0

#clicking on first product/sunglass from the result
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

#add to cart
- tapOn: "Add to Cart"

#cart & address
- runFlow: ../../../address/th-saved-address.yaml

#payment
- runFlow: ../../../payment/th-promptpay-payment.yaml



