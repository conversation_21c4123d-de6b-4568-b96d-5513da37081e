appId: com.lenskart.app

---


 #Step 1: Login to the app with Singapore country
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Thailand

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>


#Search anything using Search icon
- runFlow : ../../../search/international-search-icon.yaml

#Search keyword
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: lenskart bitz eyeglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as lenskart bitz eyeglasses

#clicking on first product
- tapOn:
    text: "lenskart bitz eyeglasses"
    index: 1

#select first product from plp
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

#selecting bitz
- runFlow: ../../../BitzFlow/with-button-select-bitz.yaml

#selecting frame only
- runFlow: ../../../power/frame-only.yaml

# cart &address
- runFlow: ../../../address/th-saved-address.yaml

#payment
- runFlow: ../../../payment/th-promptpay-payment.yaml