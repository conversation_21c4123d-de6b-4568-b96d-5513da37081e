appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#- runFlow:
  #  file: ../../../search/international-search-keyword.yaml
   # env:
   #   SEARCHKEYWORD : Eyeglass # <-- Run commands from "searchkeyword.yaml"
    #  label: Search keywrod passed as Eyeglass


#location pop up
- runFlow:
      when:
          visible: "For a better experience, your device will need to use Location Accuracy "
      file: ../../../home/<USER>

#- runFlow:
  #  file: ../../../search/international-search-keyword.yaml
  #  env:
    #  SEARCHKEYWORD: 219804 # <-- Run commands from "searchkeyword.yaml"
    #  label: Search keywrod passed as PID

#click on product
#click on product
#- tapOn:
  #  id: "com.lenskart.app:id/ivTrailingIcon"

#select tab
- tapOn: "Classic"
#select eyeglass
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- runFlow: ../../../plp/uae-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- runFlow:
      file: ../../../power/optimized-eyeglass-with-power.yaml
      env:
          lensType: "Anti-Glare Premium"
- runFlow:
    file: ../../../addPower/international-saved-powers.yaml

- waitForAnimationToEnd
- tapOn:
    id: "com.lenskart.app.store:id/proceed_icon"
    index: 0
- runFlow: ../../../payment/international-c-c-details.yaml
