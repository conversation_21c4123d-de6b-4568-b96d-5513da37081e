appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#- runFlow:
   #   file: ../../../search/international-search-keyword.yaml
    #  env:
        #  SEARCHKEYWORD: 230543 # <-- Run commands from "searchkeyword.yaml"
       #   label: Search keywrod passed as PID

#click on product
#- tapOn:
  #  id: "com.lenskart.app:id/ivTrailingIcon"

- tapOn: "Classic"

#select eyeglass
- tapOn:
     id: "com.lenskart.app:id/image"
     index: 1

- runFlow: ../../../plp/uae-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- tapOn:
    text: "Frame Only"
    optional: true
# Wait for any animations to complete
- waitForAnimationToEnd
# Handle address selection with better error handling
#select address
- tapOn: "Select address"
- waitForAnimationToEnd
#address
- tapOn:
    id: "com.lenskart.app.store:id/proceed_icon"
    index: 0

- runFlow: ../../../payment/international-c-c-details.yaml