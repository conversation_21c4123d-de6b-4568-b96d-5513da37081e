appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#- runFlow:
  #  file: ../../../search/international-search-keyword.yaml
  #  env:
  #    SEARCHKEYWORD : Full rim Sunglasses For Men  # <-- Run commands from "searchkeyword.yaml"
  #    label: Search keywrod passed as Full rim Sunglasses For Men


#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- tapOn: "Classic"
#select sunglass
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2
- runFlow: ../../../plp/uae-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- tapOn: "Without Power"
#select address
- tapOn: "Select address"
- waitForAnimationToEnd
- tapOn:
    id: "com.lenskart.app.store:id/proceed_icon"
    index: 0
- runFlow: ../../../payment/international-c-c-details.yaml

