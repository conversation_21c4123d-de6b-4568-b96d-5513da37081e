appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#- runFlow:
  #  file: ../../../search/international-search-keyword.yaml
   # env:
    #  SEARCHKEYWORD : Full rim Sunglasses For Men  # <-- Run commands from "searchkeyword.yaml"
     # label: Search keywrod passed as Full rim Sunglasses For Men

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#select tab
- tapOn: "Classic"
#select sunglass
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2
#select product on plp
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2
- runFlow: ../../../pdp/uae-pdp.yaml
- runFlow:
      file: ../../../power/optimizes-sunglasses-with-power.yaml
      env:
          lensType: "Brown Tinted Polarized Power Lenses"
- runFlow:
    file: ../../../addPower/international-saved-powers.yaml
#select address
- tapOn: "Select address"
- waitForAnimationToEnd
- tapOn:
    id: "com.lenskart.app.store:id/proceed_icon"
    index: 0
- runFlow: ../../../payment/international-c-c-details.yaml
