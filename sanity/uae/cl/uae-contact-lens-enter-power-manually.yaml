
appId: com.lenskart.app

---
# Step 1: Login to the app with Singapore country
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: United Arab Emirates

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- tapOn: "Premium"
- tapOn: "Contact Lens"
#select cl
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#select cl category from bottomsheet
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
- runFlow: ../../../plp/sa-plp.yaml
- scrollUntilVisible:
    element:
      text:  "I will submit power later"
    direction: DOWN
- tapOn: "Enter Power Manually"
- runFlow: ../../../clPowerType/international-c-l-enter-power-manually.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- waitForAnimationToEnd
- tapOn: "Select address"
- runFlow:
    when:
      visible: "Submit Eye Power"
    file: ../../../clPowerType/uae-cl-submit-power-later.yaml
- waitForAnimationToEnd
- tapOn:
    id: "com.lenskart.app.store:id/info_container"
    index: 0
- runFlow: ../../../payment/international-c-c-details.yaml
