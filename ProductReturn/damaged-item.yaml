appId: com.lenskart.app

---

- runFlow: ../../login/Popup.yaml

- tapOn:
      id: "com.lenskart.app:id/iv_initials_holder"

- tapOn:
      id: "com.lenskart.app:id/image"
      index: 0

- scrollUntilVisible:
      element:
          text: "Return"
      visibilityPercentage: 80
      speed: 60

- tapOn: "Return"

- runFlow : ../../returnExchangeReasons/damaged-product-damaged-item-reason.yaml

- runFlow : ../../returnExchangeReasons/return-flow.yaml

- tapOn: "Place Refund"



