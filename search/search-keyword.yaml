appId: com.lenskart.app


---

# Wait for home screen to load completely
- waitForAnimationToEnd

# Tap on Search bar placeholder on the homepage
- tapOn:
    id: "com.lenskart.app:id/tv_label"
    optional: true

# Wait for search screen to appear


# Tap on search input field
- tapOn:
    id: "com.lenskart.app:id/edtSearch"
    optional: true

# Wait for keyboard to appear
- waitForAnimationToEnd

# Search Query - with fallback if SEARCHKEYWORD not defined
- inputText: ${SEARCHKEYWORD}

# Wait for search suggestions to appear
- waitForAnimationToEnd

# Tap on first search result/suggestion
- tapOn:
    id: "com.lenskart.app:id/query"
    index: 0
    optional: true

# Wait for search results to load
- waitForAnimationToEnd