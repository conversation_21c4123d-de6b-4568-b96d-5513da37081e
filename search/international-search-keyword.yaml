appId: com.lenskart.app


---

# Tap on Search bar placeholder on the homepage
- tapOn:
    id: "com.lenskart.app:id/search"
    optional: true


# Tap on search input field
- tapOn:
    id: "com.lenskart.app:id/edtSearch"
    optional: true

# Search Query - with fallback if SEARCHKEYWORD not defined
- inputText: ${SEARCHKEYWORD}

# Wait for search suggestions to appear
- waitForAnimationToEnd

- pressKey: Enter

