appId: com.lenskart.app
---

- runFlow: ../../login/Popup.yaml

- tapOn:
    id: "com.lenskart.app:id/iv_initials_holder"

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

- scrollUntilVisible:
    element:
      text: "Exchange"
    visibilityPercentage: 80
    speed: 60

- tapOn: "Exchange"

- runFlow : ../../returnExchangeReasons/power-issues-not-comfortable-reason.yaml

- runFlow : ../../returnExchangeReasons/exchange-flow.yaml


- tapOn: "No, this is not my power"

- tapOn: "Continue to Exchange"

- tapOn: "Enter Power Manually"

- tapOn:
    text: "Select"
    index: 0

- tapOn: "-0.25"

- tapOn:
        text: "Select"
        index: 1

- tapOn: "+0.25"

- scrollUntilVisible:
    element:
      text: "User's Name"
    visibilityPercentage: 80
    speed: 60

- tapOn: "User's Name"

- inputText: "Test Test"

- tapOn: "Enter mobile number"

- inputText: "5000000000"

- tapOn: "CONTINUE"

- tapOn: "Place Order"
