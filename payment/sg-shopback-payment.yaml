appId: com.lenskart.app

---


- scrollUntilVisible:
    element:
      text: "ShopBack"
    direction: DOWN
    timeout: 50000
    speed: 40
    visibilityPercentage: 100
    centerElement: true
- waitForAnimationToEnd

- assertVisible:
    text: "ShopBack"

#Tapping on Shopback
- tapOn:
      text: "ShopBack"
      index: 1

#Tapping On Pay Now Button
- tapOn:
    id: "com.lenskart.app:id/button_primary"

#verifying Shopback Gateway
- assertVisible: "Shopback"

#Tapping on Back Arrow
- tapOn: "Navigate up"

- tapOn: "Continue Shopping"


