appId: com.lenskart.app


env:
  CREDITCARDNO: ****************
  CARDEXPIRY: '0230'
  CVV: 123
  CARDHOLDERNAME: TEST TEST
---

# tap on Add New Credit or Debit card CTA
- tapOn: "Add Credit/Debit/ATM Cards"

# Credit card number place holder
- tapOn: "Card number"

# Enter the Credit card number as ****************
- inputText: ${CREDITCARDNO}

# Tap Card expiry date
- tapOn: "Expiry"



# Enter Card expiry date
- inputText: ${CARDEXPIRY}

# Tap on CVV placeholder
- tapOn: "CVV"

# Enter CVV
- inputText: ${CVV}

# Tap on CARD HOLDER'S full name placeholder
- tapOn: "Full Name"

- inputText: ${CARDHOLDERNAME}

# Tap on Save & Pay now CTA
- tapOn:
    point: "50%,93%"

- waitForAnimationToEnd
# It will click on Back button on top of the payment page.

#- tapOn:
#    id: "sidebar-backBtn"

# It will Cancle the payment

#- tapOn: "YES, <PERSON><PERSON><PERSON>"

# It will land back to Homepage

# It will click on Back button on top of the payment page.


- back

- back

- tapOn:
    id: "com.lenskart.app:id/iv_back"

- tapOn: "Navigate up"

- tapOn: "Image"