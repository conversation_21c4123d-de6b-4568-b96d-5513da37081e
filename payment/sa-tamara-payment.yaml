appId: com.lenskart.app

---

#Tapping on tamara
- tapOn: "<PERSON>"

#Tapping On Pay Now Button
- tapOn:
      id: "com.lenskart.app:id/button_primary"
- waitForAnimationToEnd
- tapOn:
      id: "com.android.systemui:id/back"
- waitForAnimationToEnd
- assertVisible: "Cancel Transaction?"
- tapOn: "Yes, Cancel"


#tap on view order details
#- tapOn: "View Order Details"

#tap on order id
#- tapOn:
     # id: "com.lenskart.app:id/tv_order_id"