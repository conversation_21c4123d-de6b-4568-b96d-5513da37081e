appId: com.lenskart.app

env:
  CREDITCARDNO: ****************
  CARDEXPIRY: '0230'
  CVV: 123
  CARDHOLDERNAME: TEST TEST
---
- tapOn:
    id: "com.lenskart.app:id/textinput_placeholder"

# Enter the Credit card number as ****************
- inputText: ${CREDITCARDNO}

# Tap Card expiry date
- tapOn:
    id: "com.lenskart.app:id/edt_input_expiry"

# Enter Card expiry date
- inputText: ${CARDEXPIRY}

# Tap on CVV placeholder
- tapOn:
    id: "com.lenskart.app:id/edt_input_cvv"

# Enter CVV
- inputText: ${CVV}

# Tap on CARD HOLDER'S full name placeholder
- tapOn:
    id: "com.lenskart.app:id/edt_input_full_name"

- inputText: ${CARDHOLDERNAME}

# Tap on Save & Pay now CTA
- tapOn:
    id: "com.lenskart.app:id/button_primary"

- waitForAnimationToEnd
# It will Cancle the payment
- runFlow:
    when:
      visible: 'YES, CANCEL'
    commands:
      - tapOn:
          text: "YES, <PERSON><PERSON><PERSON>"
          optional: true
#- tapOn: "YES, CANCEL"
- waitForAnimationToEnd

# It will land back to Homepage
- tapOn:
    text: "Continue Shopping"
    optional: true