appId: com.lenskart.app

---


#Tapping on grabpay
- tapOn: "GrabPay"

#Tapping On Pay Now Button
- tapOn:
      id: "com.lenskart.app:id/button_primary"

#Tapping on device Back button
- tapOn:
      id: "com.android.systemui:id/back"

- runFlow:
    when:
      visible: 'YES, CANCEL'
    commands:
      - tapOn:
          text: "YES, CANCEL"
          optional: true

- tapOn: "Continue Shopping"



