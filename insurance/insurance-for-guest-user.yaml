appId: com.lenskart.app
---

- launchApp
- tapOn: "Skip"
- tapOn: "Go to Home"
- runFlow:
    when:
      visible: "Don't allow"
    commands:
      - tapOn: "Don't allow"
#- scrollUntilVisible:
   #           element:
    #            id: "com.lenskart.app:id/image"
    #            index: 0
     #         direction: DOWN
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 3
- tapOn:
        point: "50%,22%"
- runFlow:
    when:
      visible: "skip"
    commands:
      - tapOn: "skip"
- tapOn:
        point: "50%,48%"
- tapOn:
        id: "com.lenskart.app:id/image"
        index: 1

- tapOn: "Add to Cart"
- tapOn:
      point: "50%,50%"
- tapOn:
      id: "com.lenskart.app:id/ll_layout"
      index: 0
- tapOn: "Thanks!"
- tapOn: "Login to Proceed"
- tapOn: "Enter 10-digit phone number"
- inputText: "2123000000"
- tapOn: "Get OTP"
- inputText: "7777"
- tapOn: "Thanks!"
- scrollUntilVisible:
    element:
        id: "com.lenskart.app:id/toggle_apply_remove_sc"
    direction: DOWN
- tapOn:
    id: "com.lenskart.app:id/toggle_apply_remove_sc"
- scrollUntilVisible:
    element:
      text: "Tap to avail your insurance benefits"
    direction: DOWN
- tapOn:
    text: "Tap to avail your insurance benefits"

- assertVisible: "Insurance"
- tapOn:
        id: "com.lenskart.app:id/cl_container"
        index: 0
- tapOn: "Enter Coupon Code"
- inputText: "MDBY3409DFG"
- tapOn: "Apply"
- assertVisible: "MediBuddy code applied!"
- tapOn: "Thanks!"

- scrollUntilVisible:
    element:
      id: "com.lenskart.app:id/fl_footer"
    direction: DOWN
- assertVisible:
         id: "com.lenskart.app:id/fl_footer"

- assertVisible: "₹0 • Proceed to place order"

