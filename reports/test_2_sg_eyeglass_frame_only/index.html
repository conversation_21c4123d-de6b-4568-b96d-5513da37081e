<!DOCTYPE html>
<html>
<head>
    <title>Test Report: sg_eyeglass_frame_only</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #f44336 0%, #f44336 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 2em; }
        .header p { margin: 5px 0 0 0; opacity: 0.9; }
        .status { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 5px solid #f44336; margin-bottom: 20px; }
        .log { background: #f9f9f9; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #ddd; }
        .log pre { margin: 0; white-space: pre-wrap; word-wrap: break-word; font-size: 12px; }
        .screenshots { margin: 20px 0; }
        .screenshot { margin: 15px; display: inline-block; text-align: center; }
        .screenshot img { max-width: 300px; border: 2px solid #ddd; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .screenshot p { margin: 10px 0; font-weight: bold; color: #666; }
        .no-screenshots { text-align: center; color: #666; font-style: italic; padding: 20px; }
        .back-link { margin-top: 30px; text-align: center; }
        .back-link a { color: #2196F3; text-decoration: none; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❌ Test Report: sg_eyeglass_frame_only</h1>
            <p><strong>File:</strong> sanity/sg/eyeglass/sg-eyeglass-frame-only.yaml</p>
            <p><strong>Execution Time:</strong> 2025-08-07 13:29:47</p>
        </div>
        
        <div class="status">
            <h3>❌ Test Status: FAILED</h3>
            <p>This test was executed and the result is shown above. Check the execution log below for detailed information.</p>
        </div>
        
        <div class="log">
            <h3>📋 Execution Log</h3>
            <pre>
Waiting for flows to complete...
[Failed] sg-eyeglass-frame-only (1m 33s) (Element not found: Text matching regex: No, thanks)

1/1 Flow Failed</pre>
        </div>
        
        <div class="screenshots">
            <h3>📸 Screenshots</h3>
            <div class="screenshot">
                <img src="screenshots/failure_screenshot_132945.png" alt="failure_screenshot_132945.png" onclick="window.open(this.src)">
                <p>failure_screenshot_132945.png</p>
            </div>
        </div>
        
        <div class="back-link">
            <a href="../index.html">← Back to Test Suite Summary</a>
        </div>
    </div>
</body>
</html>
