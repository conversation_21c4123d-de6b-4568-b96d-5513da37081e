appId: com.lenskart.app

---

#Asserting the Filter text on PLP
- assertVisible: "Filter"
- tapOn: "Filter"
#Validate tool bar text and <PERSON><PERSON>'s present in PLP
- assertVisible:
    id: "com.lenskart.app:id/tv_toolbar_title"
- assertVisible:
    id: "com.lenskart.app:id/image_close"
- assertVisible:
    id: "com.lenskart.app:id/btn_clear"
- assertVisible:
    id: "com.lenskart.app:id/btn_apply"
- assertVisible:
    id: "com.lenskart.app:id/text_title"
    index: 1

#Apply single filter
- tapOn: "₹2000 - ₹2999"
- tapOn:
    id: "com.lenskart.app:id/btn_apply"
    #verify the filter count on the PLP
- tapOn:
    id: "com.lenskart.app:id/text_filter_applied_count"

#Apply multiple filter
- tapOn: "Brand"
- tapOn: "Lenskart Air"
- tapOn: "Lenskart Studio"
- tapOn:
    id: "com.lenskart.app:id/btn_apply"
- assertVisible:
      id: "com.lenskart.app:id/text_filter_applied_count"

#Clear applied filter
- tapOn: "Filter"
- tapOn:
        id: "com.lenskart.app:id/btn_clear"
- tapOn:
    id: "com.lenskart.app:id/btn_apply"
