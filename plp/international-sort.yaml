appId: com.lenskart.app

---
#tap on sort
- tapOn:
    id: "com.lenskart.app:id/text_sort"

#verify on close
- assertVisible:
    id: "com.lenskart.app:id/image_close"

#verify  texts under sort
- assertVisible:
    id: "com.lenskart.app:id/text_title"

#bestseller
- assertVisible: "Bestsellers"
#tap on bestseller
- tapOn: "Bestsellers"

#tap on sort
- tapOn:
    id: "com.lenskart.app:id/text_sort"

#new arrivals
- assertVisible: "New Arrivals"
- tapOn: "New Arrivals"

#tap on sort
- tapOn:
    id: "com.lenskart.app:id/text_sort"

- assertVisible: "Price: Low to High"
- tapOn: "Price: Low to High"

#tap on sort
- tapOn:
    id: "com.lenskart.app:id/text_sort"

- assertVisible: "Price: High to Low"

- tapOn: "Price: High to Low"

# Wait for OTP screen to load
- waitForAnimationToEnd







