appId: com.lenskart.app
---

#Search Swaps eyeglass in search bar
- tapOn:
    id: "com.lenskart.app:id/iv_icon"
    index: 0

- inputText: Swaps eyeglass

- tapOn:
      id: "com.lenskart.app:id/rvSearch"

#Click on View details button
- tapOn:
      id: "com.lenskart.app:id/btn_view_details"

#Click on Add to Cart
- tapOn:
      id: "com.lenskart.app:id/btn_add_to_cart"

#Select the swaps
- tapOn:
      id: "com.lenskart.app:id/btn_select"
      index: 1

#Click on Proceed
- tapOn:
      id: "com.lenskart.app:id/try_on_view"





