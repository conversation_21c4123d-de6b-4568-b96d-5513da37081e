appId: com.lenskart.app

---

# Run login flow first
- runFlow: ../../login/optimized-login.yaml

- runFlow: ../../login/handle-permissions.yaml

#tap on men eyglasses from home
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2

#select classic eyeglass from bottomsheet
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

#assert frame banner
- assertVisible:
    id: "com.lenskart.app:id/layout_plp_fit"


# Run all PLP section flows
- runFlow: ../../plp/international-sort.yaml
- runFlow: ../../plp/international-Filter.yaml
- runFlow: ../../plp/international-view-type.yaml


