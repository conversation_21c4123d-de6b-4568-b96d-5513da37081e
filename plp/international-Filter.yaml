appId: com.lenskart.app

---

#Asserting the Filter text on PLP
- assertVisible: "Filter"
- tapOn: "Filter"
#Validate tool bar text and <PERSON><PERSON>'s present in PLP
- assertVisible:
    id: "com.lenskart.app:id/tv_toolbar_title"

#verify close button
- assertVisible:
    id: "com.lenskart.app:id/image_close"

#verify clear all button
- assertVisible:
    id: "com.lenskart.app:id/btn_clear"

#verify apply button
- assertVisible:
    id: "com.lenskart.app:id/btn_apply"

#verify frame color filter
- assertVisible: "Frame Color"

#apply single filter
- tapOn: "Frame Color"

#select first color
- tapOn:
      id: "com.lenskart.app:id/img_check"
      index: 0

#tap on apply
- tapOn: "Apply"

# Wait for OTP screen to load
- waitForAnimationToEnd

#assert applied filter count on plp
- assertVisible:
    id: "com.lenskart.app:id/text_filter_applied"

 #tap on filter
- tapOn: "Filter"

#Apply multiple filter
- assertVisible: "Brand"

- tapOn: "Brand"

- tapOn: "<PERSON>"

#apply
- tapOn: "Apply"

# Wait for OTP screen to load
- waitForAnimationToEnd

#assert applied filter count on plp
- assertVisible:
      id: "com.lenskart.app:id/text_filter_applied_count"

#Clear applied filter
- tapOn: "Filter"

#clear all filter
- tapOn:
        id: "com.lenskart.app:id/btn_clear"
#apply
- tapOn: "Apply"
