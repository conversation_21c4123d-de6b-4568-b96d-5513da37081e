appId: com.lenskart.app
---
- launchApp

- runFlow: login.yaml # <-- Run commands from "login.yaml"
- runFlow: searchkeyword.yaml # <-- Run commands from "searchkeyword.yaml"

#Asserting the tile view on PLP
- assertVisible: "Tile View"

  #click on tile view on PLP
- tapOn: "Tile View"

  #change the view to grid
- tapOn: "Grid View"

  #Asserting if grid view is applied on PLP
- assertVisible: "Grid View"

  #click on grid view
- tapOn: "Grid View"

  #click on list view
- tapOn: "List View"

  #asserting if list view is applied on PLP
- assertVisible: "List View"
