appId: com.lenskart.app

---
- runFlow: login.yaml # <-- Run commands from "login.yaml"

- runFlow: searchkeyword.yaml # <-- Run commands from "searchkeyword.yaml"

- tapOn:
    id: "com.lenskart.app:id/text_sort"

- assertVisible:
    id: "com.lenskart.app:id/text_title"

- assertVisible:
    id: "com.lenskart.app:id/image_close"

- tapOn: "Bestsellers"
- assertVisible: "Bestsellers"
- tapOn: "Bestsellers"

- tapOn: "New Arrivals"
- assertVisible:
    text: "New Arrivals"
    index: 1
- tapOn:
      text: "New Arrivals"
      index: 1
- tapOn: "Price: Low to High"
- assertVisible: "Low to High"
- tapOn: "Low to High"
- tapOn: "Price: High to Low"
- assertVisible: "High to Low"







