appId: com.lenskart.app

---


# Scroll to the "Perfect fit for you" section
- scrollUntilVisible:
    element:
      text: "Perfect fit for you"
    direction: DOWN

# Assert that the Frame Size banner is visible
- assertVisible:
    id: "com.lenskart.app:id/pdp_frame_size"

# Validate Size Guide and click on it
- assertVisible: "Size Guide"
- tapOn: "Size Guide"

# Validate contents in the Size Guide popup
- assertVisible:
    id: "com.lenskart.app:id/iv_close"
- assertVisible:
    id: "com.lenskart.app:id/image_gold_benefit"

# Close the Size Guide
- tapOn:
    id: "com.lenskart.app:id/iv_close"

# Assert frame image and dropdown are visible
- assertVisible:
    id: "com.lenskart.app:id/image_frame"
- assertVisible:
    id: "com.lenskart.app:id/image_drop_down"

# Tap on frame image and dropdown
- tapOn:
   id: "com.lenskart.app:id/image_frame"
- tapOn: "Navigate up"

# Validate description and nudge message
- assertVisible:
    id: "com.lenskart.app:id/text_description_text"
- assertVisible:
    id: "com.lenskart.app:id/text_nudge_message"
