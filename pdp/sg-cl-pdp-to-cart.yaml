appId: com.lenskart.app

---


#Tap on add to cart
- tapOn: "Add to <PERSON><PERSON>"

#verifying user is on priscription page
- tapOn:
      id: "com.lenskart.app:id/error_tittle"

#click on sph right dropdown
- tapOn:
      id: "com.lenskart.app:id/text_right_power_selector"
      index: 0

#Select power
- tapOn: "-0.50"

#click on sph left dropdown
- tapOn:
      id: "com.lenskart.app:id/text_left_power_selector"
      index: 0

#Select power
- tapOn: "-0.50"

#select no. of right box
- tapOn:
      id: "com.lenskart.app:id/text_right_power_selector"
      index: 1

#tap on number
- tapOn: "2"

#select no. of leftt box
- tapOn:
      id: "com.lenskart.app:id/text_left_power_selector"
      index: 1

#tap on number
- tapOn: "2"

#verifying lenses are available widget is showing
- assertVisible:
      id: "com.lenskart.app:id/layout_add_to_cart_different"

#add to cart
- tapOn: "Add to <PERSON><PERSON>"

# Wait for login to complete
- waitForAnimationToEnd

 #tapping on select address button
- tapOn:
      text: "Select address"
      optional: true