appId: com.lenskart.app

---

#verifying user is on pdp
- assertVisible:
    id: "com.lenskart.app:id/image"

#Scrolling down till Bitz widget
- scrollUntilVisible:
    element:
      id: "com.lenskart.app:id/text_title"
    direction: DOWN

    #verifying charm bitz widget showing or not
    #- assertVisible:
    # id: "com.lenskart.app:id/ll_container"

  #- tapOn:
  # id: "com.lenskart.app:id/text_title"
  #index: 2

#Scroll until Select charm bitz button is visible
- scrollUntilVisible:
    element:
      text: "Select Charm Bitz"
    direction: DOWN

#tapping on select charm bitz button
- tapOn: "Select Charm Bitz"

#verifying bottomsheet is coming or not by checking the bottomsheet title
- assertVisible:
    id: "com.lenskart.app:id/tv_title"

#Tapping on title and scrolling Bottomsheet till last option
- tapOn:
    id: "com.lenskart.app:id/tv_title"

- scrollUntilVisible:
    element:
      id: "com.lenskart.app:id/text_title"
    direction: DOWN

#adding last bitz option
- tapOn:
    text: "Add"
    index: 0

#selecting second bitz which is third last option
- tapOn:
    text: "Add"
    index: 2

#tapping on Select lens package button
- tapOn: "Select Lens Package"

