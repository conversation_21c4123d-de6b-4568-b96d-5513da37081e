appId: com.lenskart.app

---
#verifying user is on pdp
- assertVisible:
    id: "com.lenskart.app:id/image"

#Scrolling down till Bitz widget
- scroll:
          direction: DOWN
          visible: "com.lenskart.app:id/text_title"

#verifying charm bitz widget showing or not
- assertVisible:
    id: "com.lenskart.app:id/text_title"
    index: 2

#selecting first bitz
- tapOn:
          id: "com.lenskart.app:id/bits_selector"
          index: 0

#selecting third bitz
- tapOn:
          id: "com.lenskart.app:id/bits_selector"
          index: 2

#scrolling right till given bitz id
- scroll:
          direction: LEFT
          visible: "com.lenskart.app:id/root_card"

#selecting that same bitz
- tapOn:
          id: "com.lenskart.app:id/root_card"

#tapping on Select lenses button
- tapOn:
          id: "com.lenskart.app:id/btn_add_to_cart"
