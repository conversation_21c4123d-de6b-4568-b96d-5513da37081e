appId: com.lenskart.app

---

# Verify the rating and review section
- assertVisible:
      id: "com.lenskart.app:id/text_rating"

- assertVisible:
      id: "com.lenskart.app:id/text_rating_count"

- tapOn:
      id: "com.lenskart.app:id/lin_rating"

# Verify rating and review bottom sheet
- assertVisible: "Reviews and Ratings"

- assertVisible:
      id: "com.lenskart.app:id/iv_close"

- assertVisible:
      id: "com.lenskart.app:id/rating_cl"

- assertVisible:
      id: "com.lenskart.app:id/rating_progress_cl"

- assertVisible:
      id: "com.lenskart.app:id/images_count_heading"

- assertVisible: "User Reviews"

- tapOn:
      id: "com.lenskart.app:id/iv_close"
