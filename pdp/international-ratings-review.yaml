appId: com.lenskart.app

---

# Verify the rating and review section
- assertVisible: "Rating and Reviews"

#verify rating is visible
- assertVisible:
        id: "com.lenskart.app:id/average_rating_text"

- assertVisible:
        id: "com.lenskart.app:id/review_count_text"

#tap on read all reviews
- tapOn:
        id: "com.lenskart.app:id/btn_rating_view_all"


# Verify rating and review bottom sheet
- assertVisible: "Reviews and Ratings"

#verify user photos
- assertVisible:
        id: "com.lenskart.app:id/images_count_heading"
#verify on user reviews
- assertVisible: "User Reviews"

#close bottomsheet
- tapOn:
      id: "com.lenskart.app:id/iv_close"
