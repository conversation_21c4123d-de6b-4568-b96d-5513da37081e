appId: com.lenskart.app

---

# Verify the rating and review section
- assertVisible:
    id: "com.lenskart.app:id/lin_rating"

#tap on ratings
- tapOn:
    id: "com.lenskart.app:id/text_rating"

# Verify rating and review bottom sheet
- assertVisible: "Reviews and Ratings"

#assert review count
- assertVisible:
    id: "com.lenskart.app:id/review_count_text"

#verify on user reviews
- assertVisible: "User Reviews"

#close bottomsheet
- assertVisible:
    id: "com.lenskart.app:id/iv_close"

- tapOn:
      id: "com.lenskart.app:id/iv_close"
