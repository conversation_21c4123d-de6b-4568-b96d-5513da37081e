appId: com.lenskart.app

---


# Tap again to enter gallery view
- tapOn:
    id: "com.lenskart.app:id/image"

# Validate all gallery view elements
- assertVisible:
    id: "com.lenskart.app.ar:id/iv_back"

- assertVisible:
    id: "com.lenskart.app.ar:id/iv_go_to_wishlist"

- assertVisible:
    id: "com.lenskart.app.ar:id/iv_cart"

- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 2

- assertVisible:
    id: "com.lenskart.app:id/card_view_view"

# Exit gallery view
- tapOn:
    id: "com.lenskart.app.ar:id/iv_back"
