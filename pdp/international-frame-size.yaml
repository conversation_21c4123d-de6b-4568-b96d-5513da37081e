appId: com.lenskart.app

---


# Scroll to the "Perfect fit for you" section
- scrollUntilVisible:
    element:
      text: "Perfect fit for you"
    direction: DOWN

# Assert that the Frame Size banner is visible
- assertVisible: "Frame Size"

# Validate Size Guide and click on it
#- assertVisible: "Size Guide"
#- tapOn: "Size Guide"

# Validate contents in the Size Guide popup
#- assertVisible:
   # id: "com.lenskart.app:id/iv_close"
#- assertVisible:
  #  id: "com.lenskart.app:id/image_gold_benefit"

# Close the Size Guide
#- tapOn:
 #   id: "com.lenskart.app:id/iv_close"

# Assert frame image and dropdown are visible
- assertVisible:
    id: "com.lenskart.app:id/image_dropdown"

#click on frame size dropdown
- tapOn:
    id: "com.lenskart.app:id/image_dropdown"

#verifying available sizes in collection
- assertVisible: "Available Sizes in Collection"

#verifying ruler for precise fit
- assertVisible: "Ruler for precise fit"

#verifying face scan for easy fit
- assertVisible: "Face scan for easy fit"

#Verifying previously bought sizes widget
- runFlow:
    when:
      visible: "Previously bought sizes"
    file: ../pdp/previously-bought-size.yaml

#scrolling down to check all frame size widget
- scrollUntilVisible:
    element:
      text: "All frame sizes"
    direction: DOWN

# Tap on frame image and dropdown
- tapOn: "Navigate up"

# Validate description and nudge message
- assertVisible:
    id: "com.lenskart.app:id/text_description_text"
#- assertVisible:
  #  id: "com.lenskart.app:id/text_nudge_message"
