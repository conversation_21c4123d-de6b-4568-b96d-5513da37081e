appId: com.lenskart.app
---

- launchApp
- tapOn: "Skip"
- tapOn: "Go to Home"
- runFlow:
    when:
      visible: "Don't allow"
    commands:
      - tapOn: "Don't allow"
- assertVisible:
        id: "com.lenskart.app:id/iv_loyalty_status"
- tapOn:
            point: "65%,8%"
- assertVisible:
              id: "com.lenskart.app:id/l_gold_bottom"
- tapOn: "Buy Now"
- assertVisible:
        id: "com.lenskart.app:id/tv_toolbar_title"
- assertVisible:
              id: "com.lenskart.app:id/layout_gold_pid"
- tapOn: "Login to Proceed"
- assertVisible: "Enter 10-digit phone number"
- tapOn:
        id: "com.lenskart.app:id/tiet_text_input"
- inputText: "2123000000"
- tapOn: "Get OTP"
- assertVisible: "Enter OTP"
- tapOn: "Enter OTP"
- inputText: "7777"
- tapOn: "Thanks!"


