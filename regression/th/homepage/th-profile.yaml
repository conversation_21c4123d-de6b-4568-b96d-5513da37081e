appId: com.lenskart.app

---

#- runFlow: ../../../login/optimized-login.yaml
#- runFlow: ../../../login/handle-permissions.yaml


#tap on profile from bottombar
- tapOn: "Profile"

  #scroll down
- scrollUntilVisible:
    element:
      text: "My Eyes"
    direction: UP

#user info
- assertVisible:
    id: "com.lenskart.app:id/l_user"

#orders
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

#back from orders
- tapOn: "Navigate up"

#wallet
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

#back from wallet
- tapOn: "Navigate up"


#notification
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2
#back from notification
- tapOn:
    point: "6%,6%"

#saved power
- tapOn: "My saved powers"
- tapOn: "Navigate up"

#frame size
- tapOn: "My frame size"
- tapOn: "Navigate up"


#AR-try try on code
- assertVisible: "AR try-on 1000+ frames"

#3d models
- tapOn: "My saved 3D models"
- tapOn: "Navigate up"

#addresses
- tapOn: "My addresses"
- tapOn:
    id: "com.lenskart.app.store:id/cta_back"


  #scroll down
- scrollUntilVisible:
    element:
      text: "Logout"
    direction: DOWN

#change app language button
- assertVisible: "Change App Language"

#notifications
- assertVisible: "My notifications"

#FAQ
- assertVisible: "Frequently asked questions"

#contact us
- assertVisible: "Contact us"

#about lenskart
- assertVisible: "About Lenskart"

- assertVisible: "Terms of Use"

#rate us
- assertVisible: "Rate us"

- assertVisible: "More options"

- assertVisible: "Logout"


#back to home
- tapOn:
    id: "com.lenskart.app:id/nav_title"
    index: 0