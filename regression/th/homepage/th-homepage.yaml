appId: com.lenskart.app

---

#- runFlow: ../../../login/optimized-login.yaml
#- runFlow: ../../../login/handle-permissions.yaml

#lenskart logo
- assertVisible:
      id: "com.lenskart.app:id/iv_logo"

#search icon
- assertVisible:
      id: "com.lenskart.app:id/iv_icon"
      index: 0

#number
- assertVisible:
      id: "com.lenskart.app:id/iv_call"

#scanner
- assertVisible:
      id: "com.lenskart.app:id/iv_icon"
      index: 1

#wishlist
- assertVisible:
      id: "com.lenskart.app:id/iv_icon"
      index: 2
#cart
- assertVisible:
      id: "com.lenskart.app:id/iv_icon"
      index: 3

#buy online
- assertVisible:
      id: "com.lenskart.app:id/image"
      index: 1

#buy at store
- tapOn:
      id: "com.lenskart.app:id/image"
      index: 2

- waitForAnimationToEnd

#back
- tapOn:
      id: "com.android.systemui:id/back"

#buy on chat
- assertVisible:
      id: "com.lenskart.app:id/image"
      index: 3

#trending at lenskart banners
- assertVisible:
      id: "com.lenskart.app:id/text_heading"
      index: 0

- scrollUntilVisible:
      element:
          text: "Lenskart Promise"
      direction: DOWN

- assertVisible: "Eyeglasses"

- assertVisible:
      id: "com.lenskart.app:id/recyclerview"
      index: 2

- assertVisible: "Sunglasses"

- assertVisible:
      id: "com.lenskart.app:id/recyclerview"
      index: 3

- scrollUntilVisible:
      element:
          text: "Near by Stores"
      direction: DOWN

- tapOn: "Find Stores"

- waitForAnimationToEnd

#back
- tapOn:
      id: "com.android.systemui:id/back"


- scrollUntilVisible:
      element:
          text: "Choose your vibe"
      direction: DOWN

- assertVisible:
      id: "com.lenskart.app:id/banner_store_locator"

- scrollUntilVisible:
      element:
          text: "Lenskart Promise"
      direction: UP

#bottombar
- tapOn:
      id: "com.lenskart.app:id/item_selection_ll"
      index: 1

- tapOn: "Navigate up"

#orders
- tapOn: "Orders"

- waitForAnimationToEnd

- tapOn: "Navigate up"

#profile
- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 4
#back to home
- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 1

#AR try on
- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 0
#back
- tapOn:
      id: "com.lenskart.app.ar:id/iv_back"
