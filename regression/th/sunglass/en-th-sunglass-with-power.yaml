appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd
-
#tap on go to home
- tapOn:
    text: "Go to home"
    optional: true

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

#Search anything using Search icon
- runFlow : ../../../search/international-search-icon.yaml

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: 147675 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as PID

#select product
- tapOn:
    id: "com.lenskart.app:id/tvProductId"

#add to cart
- tapOn: "Add to Cart"

#select package
- runFlow:
    file: ../../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Brown Tinted"

- tapOn: "Proceed to <PERSON><PERSON>"


# add power by entering manually
- runFlow: ../../../addPower/add-power-enter-manully.yaml

#address
- runFlow: ../../../address/th-saved-address.yaml

#payment
- runFlow: ../../../payment/th-promptpay-payment.yaml











