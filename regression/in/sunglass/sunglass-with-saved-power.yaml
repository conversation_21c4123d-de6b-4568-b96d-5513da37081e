appId: com.lenskart.app

---


- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : With Power Sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as With Power Sunglasses
- pressKey: Enter

#It will select the 1st product from Sunglasses PLP

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#Sunglasses will be added to cart.

- tapOn: "Add to Cart"

- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Brown Tinted"

- waitForAnimationToEnd

- runFlow: ../../addPower/add-power-saved-powers.yaml

- runFlow: ../../address/saved-address.yaml

- runFlow: ../../payment/credit-card-details.yaml
