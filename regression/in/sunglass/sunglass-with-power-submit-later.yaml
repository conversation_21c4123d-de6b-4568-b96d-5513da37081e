appId: com.lenskart.app

---

- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Sunglasses For Men # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Sunglasses For Men

#It will select the 1st product from Sunglasses PLP

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#Sunglasses will be added to cart.

- tapOn: "Add to Cart"

- runFlow: ../../power/with-power-sunglass-green-tinted-power-lenses.yaml

- waitForAnimationToEnd

- runFlow: ../../addPower/add-power-submit-later.yaml

- runFlow: ../../address/add-new-address-current-loaction.yaml

- runFlow: ../../payment/credit-card-details.yaml