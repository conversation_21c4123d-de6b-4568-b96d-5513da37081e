appId: com.lenskart.app

---

- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Withpower sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Withpower sunglasses

#It will select the 1st product from Sunglasses PLP

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#Sunglasses will be added to cart.

- tapOn: "Add to Cart"

- runFlow: ../../power/with-power-sunglass-green-tinted-power-lenses.yaml

- waitForAnimationToEnd

- runFlow: ../../addPower/home-eye-test.yaml

- runFlow: ../../address/saved-address.yaml

- runFlow: ../../payment/credit-card-details.yaml

