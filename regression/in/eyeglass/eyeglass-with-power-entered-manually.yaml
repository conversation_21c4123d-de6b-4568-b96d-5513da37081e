appId: com.lenskart.app

---

- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Withpower Eyeglass  # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Withpower Eyeglass
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
- tapOn: "Add to Cart"  # Intentional failure for testing

- runFlow:
    file: ../../power/optimized-eyeglass-with-power.yaml
    env:
      lensType: "Thin BLU Screen Lenses"

- waitForAnimationToEnd

- runFlow: ../../addPower/add-power-enter-manully.yaml

- runFlow: ../../address/saved-address.yaml

- runFlow: ../../payment/credit-card-details.yaml

