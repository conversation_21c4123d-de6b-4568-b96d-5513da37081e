appId: com.lenskart.app

---
- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: Frame only Eyeglass # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Frame only Eyeglass
- pressKey: Enter
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
- tapOn: "Add to Cart"
#WithoutPower - It will select Frame only.

- tapOn:
    text: "Frame Only"
    optional: true

# Wait for any animations to complete
- waitForAnimationToEnd

# Handle address selection with better error handling
- runFlow:
    when:
      visible: "Select Address"
    commands:
      - runFlow: ../../address/add-new-address-current-loaction.yaml

# Alternative: Handle if already on address screen
- runFlow:
    when:
      visible: "Add New Address"
    commands:
      - runFlow: ../../address/add-new-address-current-loaction.yaml

- runFlow: ../../payment/credit-card-details.yaml







