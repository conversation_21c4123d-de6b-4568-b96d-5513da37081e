appId: com.lenskart.app

env:
  SEARCHKEYWORD: "aqualens"

---
# Step 1: Login to the app
- runFlow: ../../login/popup.yaml


- scrollUntilVisible:
    element:
      text: "Contact Lenses & Accessories"
    direction: DOWN

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 13

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0


- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

- tapOn:
    text: "Add to Cart"
    optional: true


- runFlow: ../../address/saved-address.yaml


- runFlow: ../../payment/credit-card-details.yaml

