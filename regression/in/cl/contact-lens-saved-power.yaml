appId: com.lenskart.app

env:
  SEARCHKEYWORD: "aqualens"

---
# Step 1: Login to the app
- runFlow: ../../login/popup.yaml

# Wait for app to stabilize after login


# Step 2: Search for contact lens
- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: "aqualens"

# Wait for search results


# Press Enter to confirm search
- pressKey: Enter

# Wait for product list to load

# Step 3: Select contact lens product
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
    optional: true

# Wait for product details to load


# Step 4: Add to cart
- tapOn:
    text: "Add to Cart"
    optional: true

# Wait for cart options


# Step 5: Configure power settings
- runFlow: ../../clPowerType/c-l-used-saved-power.yaml

# Wait for power configuration


# Step 6: Add to cart again (after power selection)
- tapOn:
    text: "Add to Cart"
    optional: true

# Wait for cart options


# Step 7: Handle skip to cart option
- runFlow:
    when:
      visible: 'Skip to cart'
    commands:
      - tapOn: "Skip to cart"

# Wait for address screen


# Step 8: Select address
- runFlow: ../../address/saved-address.yaml

# Wait for payment screen


# Step 9: Process payment (will cancel at the end)
- runFlow: ../../payment/credit-card-details.yaml

