appId: com.lenskart.app

---
- launchApp

- runFlow: ../login/login1.yaml

- runFlow:
    file: ../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: Full rim eyeglasses
      label: Search for Eyeglass

# Tap on the first product (common precondition before PDP flows)
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1

# Run PDP section flows one by one

- runFlow: ./GalleryView.yaml

- runFlow: ./Ratings_Review.yaml

- runFlow: ./FrameColor.yaml

- runFlow: ./FrameSize.yaml