appId: com.lenskart.app

---
- launchApp

- runFlow: ../../login/popup.yaml

#- runFlow: ../../profile/my-profile-validation.yaml

- tapOn:
    id: "com.lenskart.app:id/iv_loyalty_status"

- assertVisible: "Savings"

- tapOn: "Navigate up"

- tapOn:
    id: "com.lenskart.app:id/iv_icon"
    index: 0

- assertVisible: "Wishlist"

- waitForAnimationToEnd

- tapOn: "Navigate up"

- tapOn: "Stores"

- assertVisible: "Find nearby stores"

- tapOn: "Try At Home"

- assertVisible: "Lenskart at Home"

- tapOn:
    point: "10%,8%"

- tapOn: "Leave"

- tapOn:
    text: "Home"
    index: 0

- scrollUntilVisible:
    element:
      text: "Eyeglasses"
    direction: up

