appId: com.lenskart.app

---
- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: Frame only Eyeglass # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Frame only Eyeglass
- pressKey: Enter
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
- tapOn: "Add to Cart"
#WithoutPower - It will select Frame only.

- tapOn:
    text: "Frame Only"
    optional: true

- waitForAnimationToEnd

- tapOn:
    point: "7%,8%"

- tapOn:
    id: "com.lenskart.app:id/iv_back"

- tapOn: "Navigate up"

- tapOn: "Image"

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Withpower sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Withpower sunglasses


- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#Sunglasses will be added to cart.

- tapOn: "Add to <PERSON>t"

#It will select the sunglasses - Pink Tinted with power package.
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Pink Tinted"

- runFlow: ../../addPower/add-power-enter-manully.yaml

- waitForAnimationToEnd

- tapOn:
    point: "7%,8%"

- tapOn:
    point: "7%,8%"

- tapOn: "Navigate up"

- tapOn: "Image"

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: "contact lens-spherical" # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as contact lens-spherical

# Wait for search results


# Press Enter to confirm search
- pressKey: Enter

# Wait for product list to load

# Step 3: Select contact lens product
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
    optional: true

# Wait for product details to load


# Step 4: Add to cart
- tapOn:
    text: "Add to Cart"
    optional: true

# Wait for cart options


# Step 5: Configure power settings
- runFlow: ../../clPowerType/c-l-used-saved-power.yaml

# Wait for power configuration


# Step 6: Add to cart again (after power selection)
- tapOn:
    text: "Add to Cart"
    optional: true




# Step 7: Handle skip to cart option
- runFlow:
    when:
      visible: 'Skip to cart'
    commands:
      - tapOn: "Skip to cart"


- runFlow: ../../address/saved-address.yaml

- runFlow: ../../payment/credit-card-details.yaml