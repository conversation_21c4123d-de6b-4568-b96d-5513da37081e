appId: com.lenskart.app

---
- runFlow: ../../login/popup.yaml

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: Frame only Eyeglass # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Frame only Eyeglass
- pressKey: Enter
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
- tapOn: "Add to Cart"
#WithoutPower - It will select Frame only.

- tapOn:
    text: "Frame Only"
    optional: true

- waitForAnimationToEnd

- tapOn:
    point: "7%,8%"

- tapOn:
    id: "com.lenskart.app:id/iv_back"

- tapOn: "Navigate up"

- tapOn: "Image"

- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Withpower sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Withpower sunglasses


- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#Sunglasses will be added to cart.

- tapOn: "Add to <PERSON><PERSON>"
#It will select the sunglasses - Pink Tinted with power package.
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Pink Tinted"

- runFlow: ../../addPower/add-power-enter-manully.yaml

- runFlow: ../../address/saved-address.yaml

- runFlow: ../../payment/credit-card-details.yaml