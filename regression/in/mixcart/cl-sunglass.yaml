appId: com.lenskart.app

env:
  SEARCHKEYWORD: "aqualens"

---
# Step 1: Login to the app
- runFlow: ../../login/popup.yaml

# Wait for app to stabilize after login


# Step 2: Search for contact lens
- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD: "aqualens"

# Wait for search results


# Press Enter to confirm search
- pressKey: Enter

# Wait for product list to load

# Step 3: Select contact lens product
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
    optional: true

# Wait for product details to load


# Step 4: Add to cart
- tapOn:
    text: "Add to Cart"
    optional: true

# Wait for cart options


# Step 5: Configure power settings
- runFlow: ../../clPowerType/c-l-used-saved-power.yaml

# Wait for power configuration


# Step 6: Add to cart again (after power selection)
- tapOn:
    text: "Add to Cart"
    optional: true


- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Withpower sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Withpower sunglasses

#It will select the 1st product from Sunglasses PLP

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 1
#Sunglasses will be added to cart.

- tapOn: "Add to Cart"
#It will select the sunglasses - Pink Tinted with power package.
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Pink Tinted"

- waitForAnimationToEnd

- runFlow: ../../addPower/add-power-enter-manully.yaml

- runFlow: ../../address/saved-address.yaml

- runFlow: ../../payment/credit-card-details.yaml