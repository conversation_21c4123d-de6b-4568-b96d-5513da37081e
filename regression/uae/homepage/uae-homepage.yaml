appId: com.lenskart.app

---

- runFlow: ../../../login/optimized-login.yaml
- runFlow: ../../../login/handle-permissions.yaml

- tapOn:
    id: "com.lenskart.app:id/iv_icon"
    index: 0
- tapOn: "Navigate up"
- tapOn:
    id: "com.lenskart.app:id/iv_icon"
    index: 1
- tapOn:
    point: "8%,8%"
- tapOn:
    id: "com.lenskart.app:id/tab_text"
    index: 1
- tapOn:
    id: "com.lenskart.app:id/tab_text"
    index: 2
- tapOn:
    id: "com.lenskart.app:id/tab_text"
    index: 0
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0
- tapOn: "Navigate up"

- scrollUntilVisible:
    element:
      text: "Sunglasses"
    direction: DOWN

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2
- tapOn:
    id: "com.lenskart.app:id/iv_close"

- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 3
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 4
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 5
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 6
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 7
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 8
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 9
- scrollUntilVisible:
    element:
      text: "Screen Glasses"
    direction: DOWN

- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 8
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 9
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 10
- assertVisible:
    id: "com.lenskart.app:id/image"
    index: 11

