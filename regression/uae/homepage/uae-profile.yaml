appId: com.lenskart.app

---

- runFlow: ../../../login/optimized-login.yaml
- runFlow: ../../../login/handle-permissions.yaml

- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 2
- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 3
- tapOn: "Navigate up"
- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 4
- tapOn: "Join Gold MAX"
- tapOn: "Navigate up"
- tapOn:
      id: "com.lenskart.app:id/image"
      index: 0
- tapOn: "Navigate up"
- tapOn:
      id: "com.lenskart.app:id/image"
      index: 1
- tapOn: "Navigate up"
- tapOn:
      id: "com.lenskart.app:id/image"
      index: 2
- tapOn: "Navigate up"
- tapOn: "My saved powers"
- tapOn: "Navigate up"
- scrollUntilVisible:
      element:
          text: "Buy in Store"
      direction: DOWN
- tapOn: "My saved 3D models"
- tapOn: "Navigate up"
- assertVisible: "Find nearby store"
- scrollUntilVisible:
      element:
          text: "Get Help"
      direction: DOWN
- tapOn: "My addresses"
- tapOn:
        id: "com.lenskart.app.store:id/cta_back"
- tapOn: "My notifications"
- tapOn:
        point: "8%,8%"
- tapOn: "Manage notifications"
- tapOn:
        point: "8%,8%"
- tapOn: "Frequently asked questions"
- tapOn:
        point: "8%,8%"
- assertVisible: "Contact us"
