appId: com.lenskart.app

---

- assertVisible:
    id: "com.lenskart.app.store:id/cta_back"
- assertVisible: "Select address"
- assertVisible: "Add New Address"
- assertVisible: "Saved Addresses"
- assertVisible:
    text: "Delete"
    index: 0
- assertVisible:
    id: "com.lenskart.app:id/btn_action"
    index: 1
- tapOn:
    id: "com.lenskart.app:id/btn_action"
    index: 1
- assertVisible: "Edit delivery address"
- assertVisible:
    text: "Home"
    index: 0
- assertVisible: "Work"
- assertVisible: "Friends & Family"
- assertVisible: "House/Villa no / Building Name*"
- assertVisible: "Area / Street*"
- assertVisible: "Landmark (optional)"
- assertVisible: "City*"
- assertVisible: "Name*"
- assertVisible: "Phone number*"
- assertVisible: "Save address & Proceed"
- tapOn:
    id: "com.lenskart.app.store:id/cta_back"
- tapOn:
    id: "com.lenskart.app:id/btn_action"
    index: 2
- assertVisible: "Confirm Delete"
- assertVisible:
    id: "com.lenskart.app:id/btn_dismiss"
- assertVisible: "Are you sure you want to delete this address?"
- assertVisible: "Cancel"
- tapOn: "Delete"
