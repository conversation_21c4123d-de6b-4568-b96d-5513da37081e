appId: com.lenskart.app
env:
  PID: 222695

---

# Run login flow first
- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

#tapping on Search icon (try multiple approaches)
- tapOn:
    id: "com.lenskart.app:id/search"
    optional: true

#entering the PID
- inputText: ${PID}

#tap on product from result
- tapOn:
    id: "com.lenskart.app:id/ivTrailingIcon"

# Run PDP section flows one by one
- runFlow: ../../../pdp/gallery-view.yaml

#verifying frame color
- runFlow: ../../../pdp/frame-color.yaml

#verifying frame size
- runFlow: ../../../pdp/uae-frame-size.yaml

- runFlow: ../../../pdp/international-similar-products.yaml

#scrolling down till ratings and reviwes
- scrollUntilVisible:
    element:
      text: "Rating and Reviews"
    direction: DOWN
- tapOn: "Write a Review"
- tapOn:
    point: "8%,8%"
- scrollUntilVisible:
    element:
      text: "User Reviews"
    direction: DOWN
- assertVisible:
    id: "com.lenskart.app:id/btn_rating_view_all"


