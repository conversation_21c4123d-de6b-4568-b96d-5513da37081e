appId: com.lenskart.app

---

- runFlow: ../../../login/optimized-login.yaml

- runFlow: ../../../login/handle-permissions.yaml

- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Eyeglass # <-- Run commands from "search keyword.yaml"
      label: Search keywrod passed as Eyeglass
- runFlow: ../../../plp/uae-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- tapOn:
    text: "Frame Only"
    optional: true

- pressKey: "BACK"
- pressKey: "BACK"
- pressKey: "BACK"
- pressKey: "BACK"

- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Full rim Sunglasses For Men  # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Full rim Sunglasses For Men

- runFlow: ../../../plp/uae-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- runFlow:
    file: ../../../power/uae-Sunglass-With-Power.yaml
- runFlow:
    file: ../../../addPower/add-power-saved-powers.yaml
- tapOn: "Select Address"

- runFlow:
    file: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/international-c-c-details.yaml
