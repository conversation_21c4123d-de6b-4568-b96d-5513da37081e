appId: com.lenskart.app

---
- assertVisible:
    id: "com.lenskart.app:id/cta_back"
- assertVisible:
    id: "com.lenskart.app:id/button_view_bill"
- assertVisible: "Cards"
- assertVisible:
    id: "com.lenskart.app:id/container"
    index: 2
- assertVisible:
    id: "com.lenskart.app:id/rv_bank_offers"
- assertVisible:
    id: "com.lenskart.app:id/textinput_placeholder"
- assertVisible: "Expiry (MM/YY)"
- assertVisible: "CVV"
- assertVisible:
    id: "com.lenskart.app:id/edt_input_full_name"
- assertVisible:
    id: "com.lenskart.app:id/cb_secure_card"
- assertVisible: "Buy Now Pay Later"
- assertVisible:
    id: "com.lenskart.app:id/container"
    index: 4
- tapOn:
      id: "com.lenskart.app:id/radio_button_payment"
- scrollUntilVisible:
    element:
      text: "Cash On Delivery"
    direction: DOWN
- assertVisible:
    text: "Cash On Delivery"
    index: 1
- tapOn:
    text: "Cash On Delivery"
    index: 1
- assertVisible:
    id: "com.lenskart.app:id/button_primary"