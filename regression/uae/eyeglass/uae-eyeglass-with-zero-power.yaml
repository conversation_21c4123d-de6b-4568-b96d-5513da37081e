appId: com.lenskart.app

---

- runFlow: ../../../login/optimized-login.yaml
- runFlow: ../../../login/handle-permissions.yaml
- runFlow:
    file: ../../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : Eyeglass # <-- Run commands from "search keyword.yaml"
      label: Search keywrod passed as Eyeglass
- runFlow: ../../../plp/uae-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- runFlow: ../../../power/uae-eyeglass-zero-power.yaml

# Wait for any animations to complete
- waitForAnimationToEnd
# Handle address selection with better error handling
- tapOn: "Select Address"
- runFlow:
    file: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/international-c-c-details.yaml
