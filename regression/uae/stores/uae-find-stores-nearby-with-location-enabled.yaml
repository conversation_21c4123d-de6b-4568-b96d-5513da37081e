appId: com.lenskart.app

---

- scrollUntilVisible:
    element:
      text: "Nearby stores"
    direction: DOWN
    speed: 30
    timeout: 30000
    visibilityPercentage: 50
    waitToSettleTimeoutMs: 500
- tapOn: "Find stores"
- assertVisible:
    id: "com.lenskart.app:id/tv_search_bar_heading"
- assertVisible:
    id: "com.lenskart.app:id/cl_help"
- assertVisible:
    id: "com.lenskart.app:id/search_layout"
- assertVisible:
    id: "com.lenskart.app:id/text_call_store"
- assertVisible:
    id: "com.lenskart.app:id/text_store_directions"

- scrollUntilVisible:
    element:
      text: "Sahara Centre"
    direction: DOWN
    speed: 30
    timeout: 30000
    visibilityPercentage: 50
    waitToSettleTimeoutMs: 500
- tapOn: "Book Appointment"
- assertVisible:
    id: "com.lenskart.app:id/change_button"
- tapOn:
    id: "com.lenskart.app:id/layout_date"
    index: 3

- scrollUntilVisible:
    element:
      text: "Pick a time"
    direction: DOWN
    speed: 80
    timeout: 10000
    visibilityPercentage: 100
    centerElement: true
- tapOn:
    id: "com.lenskart.app:id/tv_time"
    index: 5
- tapOn: "Confirm & Book"
- tapOn:
    id: "com.lenskart.app:id/appointmentDetailsLayout"
    index: 1
- tapOn:
    id: "com.lenskart.app:id/btn_continue"
