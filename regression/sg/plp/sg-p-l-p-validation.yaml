appId: com.lenskart.app

---

- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#tap on men eyglasses from home
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 2

#select classic eyeglass from bottomsheet
- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

#assert frame banner
- assertVisible:
    id: "com.lenskart.app:id/layout_plp_fit"


# Run all PLP section flows
- runFlow: ../../../plp/international-sort.yaml
- runFlow: ../../../plp/international-Filter.yaml
- runFlow: ../../../plp/international-view-type.yaml


