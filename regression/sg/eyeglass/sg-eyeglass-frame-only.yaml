appId: com.lenskart.app

env:
  MOBILENO: 2123000009
  OTP: 7777
  SEARCHKEYWORD: "Eyeglass"

---
- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow: ../../../search/sg-search-eyeglass.yaml

# Wait for search results
- waitForAnimationToEnd

- tapOn:
    text: "Select Lenses"
    optional: true

# Wait for lens options


# Step 7: Choose Frame Only option
- tapOn:
    text: "Frame Only"
    optional: true


# Step 8: Handle address selection
- tapOn:
    text: "Select Address"
    optional: true


# Step 9: Use saved address
- runFlow: ../../../address/saved-address.yaml

- tapOn: "Credit/Debit Card"

# Step 10: Process payment
- runFlow: ../../../payment/international-c-c-details.yaml

