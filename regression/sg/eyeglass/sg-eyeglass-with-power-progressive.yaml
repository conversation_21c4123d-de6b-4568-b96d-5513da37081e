appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>


- runFlow: ../../../pdp/sg-pdp/sg-progressive-pdp.yaml

- runFlow: ../../../pdp/sg-package-screen/sg-eyeglass-progressive.yaml

- runFlow:
    file: ../../../progressive/optimized-progressive.yaml
    env:
      lensType: "Tokai Progressive"

- tapOn: "Skip coating addition"

- runFlow: ../../../cart/sg-cart/sg-progressive-product-cart.yaml

- runFlow: ../../../address/sg-studioflow.yaml

- runFlow: ../../../payment/sg-grabpay-payment.yaml
