appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: 220217 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as PID

#tap on first result
- tapOn:
    id: "com.lenskart.app:id/rvSearch"

#click on select lenses
- runFlow: ../../../pdp/sg-package-screen/sg-eyeglass-progressive.yaml


- runFlow:
    file: ../../../power/optimized-eyeglass-with-power.yaml
    env:
      lensType: "Myopia Control"

#submit power later
- tapOn: "Submit Power Later"

- waitForAnimationToEnd

#back from cart
- tapOn:
    point: "6%,6%"

- tapOn:
    id: "com.lenskart.app:id/iv_back"

#back to home
- tapOn:
    id: "com.lenskart.app:id/imgStartIcon"

#search by keyword
- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: sunglasses # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as Sunglasses

#clicking on sunglasses text
- tapOn:
    id: "com.lenskart.app:id/query"
    index: 0

#select first product on plp
- runFlow: ../../../plp/international-plp.yaml


- waitForAnimationToEnd

- runFlow: ../../../pdp/sg-package-screen/sg-Sunglass-with-power.yaml

- runFlow:
    file: ../../../power/optimized-eyeglass-with-power.yaml
    env:
      lensType: "Brown Tinted"

- runFlow: ../../../addPower/add-power-saved-powers.yaml

- runFlow: ../../../cart/sg-cart/sg-progressive-product-cart.yaml

- runFlow: ../../../address/sg-studioflow.yaml

- runFlow: ../../../payment/sg-atome-payment.yaml