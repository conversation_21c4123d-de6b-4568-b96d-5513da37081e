appId: com.lenskart.app
env:
  PID: 222695

---
- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#tapping on Search icon (try multiple approaches)
- tapOn:
    id: "com.lenskart.app:id/search"
    optional: true

#entering the PID
- inputText: ${PID}

#tap on product from result
- tapOn:
    id: "com.lenskart.app:id/ivTrailingIcon"

# Run PDP section flows one by one
- runFlow: ../../../pdp/gallery-view.yaml

#verifying frame color
- runFlow: ../../../pdp/frame-color.yaml

#verifying frame size
- runFlow: ../../../pdp/international-frame-size.yaml

#verifying islandwise delivery widget
- runFlow: ../../../pdp/sg-islandwise-delivery.yaml

#scrolling down till ratings and reviwes
- scrollUntilVisible:
    element:
      text: "User Reviews"
    direction: DOWN

- runFlow: ../../../pdp/international-ratings-review.yaml

- runFlow: ../../../pdp/international-similar-products.yaml

