appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd

- tapOn:
        text: "Go to home"
        optional: true


# Step 1: Login to the app with Singapore country
- runFlow:
    file: ../../../login/international-login.yaml
    env:
      COUNTRY: Singapore

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

#user
- assertVisible:
      id: "com.lenskart.app:id/iv_initials_holder"
#islandwise
- assertVisible: "Islandwide"
#gold
- assertVisible:
      id: "com.lenskart.app:id/iv_loyalty_status"

#wishlist
- assertVisible:
      id: "com.lenskart.app:id/iv_icon"
      index: 0

#cart
- assertVisible:
      id: "com.lenskart.app:id/iv_icon"
      index: 1

- assertVisible:
      id: "com.lenskart.app:id/search_content"

- tapOn: "On Sale"

- tapOn: "Contact Lens"

- tapOn: "Classic"

- scrollUntilVisible:
      element:
          text: "For You"
      direction: LEFT

- tapOn: "Premium"

- tapOn: "For You"

- scrollUntilVisible:
      element:
          text: "All"
      direction: RIGHT

- assertVisible: "Eyeglasses"

- assertVisible: "Contact Lenses"

- assertVisible: "Sunglasses"

- scrollUntilVisible:
      element:
          text: "Keep shopping for your favorites"
      direction: DOWN

- scrollUntilVisible:
      element:
          text: "View all"
      direction: LEFT

- tapOn: "View all"

- waitForAnimationToEnd

- tapOn: "Navigate up"


- scrollUntilVisible:
      element:
          text: "Near by Stores"
      direction: DOWN

- tapOn: "Find Stores"

- waitForAnimationToEnd
#back
- tapOn:
      id: "com.android.systemui:id/back"


- scrollUntilVisible:
      element:
          text: "Lenskart Promise"
      direction: DOWN


#bottombar
- tapOn:
      id: "com.lenskart.app:id/item_selection_ll"
      index: 1


#orders
- tapOn: "Orders"

- waitForAnimationToEnd

- tapOn: "Navigate up"

#profile
- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 4
#back to home
- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 1

#AR try on
- tapOn:
      id: "com.lenskart.app:id/nav_icon_image"
      index: 0
#back
- tapOn:
      id: "com.lenskart.app.ar:id/iv_back"
