appId: com.lenskart.app

---
- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true

#location pop up
- runFlow:
    when:
      visible: "For a better experience, your device will need to use Location Accuracy "
    file: ../../../home/<USER>

- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD: 224465 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as PID

#click on product
- tapOn:
    id: "com.lenskart.app:id/tvProductId"

- runFlow: ../../../pdp/sg-bitz-widget/sg-with-button-select-bitz.yaml

- runFlow: ../../../pdp/sg-package-screen/sg-sunglass-without-power.yaml

- runFlow: ../../../cart/sg-cart/sg-regular-product-cart.yaml

- runFlow: ../../../address/sg-saved-address.yaml

- runFlow: ../../../payment/sg-atome-payment.yaml