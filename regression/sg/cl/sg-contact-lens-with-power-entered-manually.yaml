appId: com.lenskart.app

---


- launchApp
- waitForAnimationToEnd

- tapOn:
    text: "Go to home"
    optional: true


- runFlow:
    file: ../../../search/international-search-keyword.yaml
    env:
      SEARCHKEYWORD : 224271 # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as 224271

- tapOn:
    id: "com.lenskart.app:id/tvProductId"

- tapOn:
    id: "com.lenskart.app:id/image"
    index: 0

- tapOn: "Add to Cart"

- runFlow: ../../../clPowerType/c-l-enter-power-manually.yaml

#tapping on select address button
- tapOn:
    id: "com.lenskart.app:id/btn_continue"

- runFlow: ../../../address/sg-saved-address.yaml

- runFlow: ../../../payment/sg-shopback-payment.yaml