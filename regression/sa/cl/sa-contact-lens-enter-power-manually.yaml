appId: com.lenskart.app

---

- runFlow: ../../../login/optimized-login.yaml
- runFlow: ../../../login/handle-permissions.yaml

- scrollUntilVisible:
    element:
      text: "Contact Lenses"          # or use textMatches
    direction: DOWN
    speed: 60
    timeout: 20000
    visibilityPercentage: 50
    waitToSettleTimeoutMs: 500
    centerElement: true

- runFlow: ../../../home/<USER>
- runFlow: ../../../plp/sa-plp.yaml
- runFlow: ../../../pdp/uae-pdp.yaml
- runFlow: ../../../clPowerType/c-l-enter-power-manually.yaml
- tapOn: "Select Address"
- runFlow: ../../../address/uae-new-saved-address.yaml
- runFlow: ../../../payment/international-c-c-details.yaml
