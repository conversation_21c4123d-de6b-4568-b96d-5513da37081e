appId: com.lenskart.app

---

- runFlow: ../../login/Popup.yaml

- runFlow:
    file:  ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : bausch  # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as bausch

- tapOn:
    id: "com.lenskart.app:id/rv_plp_banner"



- scrollUntilVisible:
    element:
      text: "Enter Power Manually"
    visibilityPercentage: 80
    speed: 60

- tapOn: "Enter Power Manually"

- tapOn:
    text: "Select"
    index: 0

- tapOn:
    id: "com.lenskart.app:id/layout_selection"
    index: 2

- tapOn:
    text: "Select"
    index: 1

- tapOn:
    id: "com.lenskart.app:id/layout_selection"
    index: 2


- tapOn:
    id: "com.lenskart.app:id/btn_add_to_cart"
    label: tap on Add to Cart cta


- tapOn: "Select address"

- tapOn:
    text: "Test Order, Abc"
    index: 0


- runFlow:  ../../payment/credit-card-detail-cl.yaml

