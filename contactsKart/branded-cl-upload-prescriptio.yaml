appId: com.lenskart.app

---

- runFlow:  ../../login/Popup.yaml



- runFlow:
    file: ../../search/search-keyword.yaml
    env:
      SEARCHKEYWORD : bausch  # <-- Run commands from "searchkeyword.yaml"
      label: Search keywrod passed as bausch


- tapOn:
    id: "com.lenskart.app:id/rv_plp_banner"

- scrollUntilVisible:
    element:
      text: "Upload Prescription"
    visibilityPercentage: 80
    speed: 60
- tapOn:
    text: "Upload Prescription"
    index: 0

- tapOn:
    id: "com.lenskart.app:id/btn_upload_prescription"

- tapOn:
    id: "com.lenskart.app:id/layout_camera"

- tapOn: "Shutter"

- tapOn: "Done"
- tapOn:
    id: "com.lenskart.app:id/btn_add_to_cart"
    label: tap on Add to Cart cta


- tapOn: "Select address"

- tapOn:
    text: "Test Order, Abc"
    index: 0


- runFlow:
    file: ../../payment/credit-card-detail-cl.yaml

