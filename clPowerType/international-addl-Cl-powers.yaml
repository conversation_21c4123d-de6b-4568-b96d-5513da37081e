appId: com.lenskart.app

---
#Left spherical power
- tapOn:
      id: "com.lenskart.app:id/text_right_power_selector"
      index: 0

- tapOn:
      id: "com.lenskart.app:id/progressBar"
      index: 3

#right spherical Power
- tapOn:
      id: "com.lenskart.app:id/text_left_power_selector"
      index: 0

- tapOn:
      id: "com.lenskart.app:id/progressBar"
      index: 3

#Addl Power
- tapOn:
      id: "com.lenskart.app:id/text_right_power_selector"
      index: 1
#low
- tapOn:
      id: "com.lenskart.app:id/progressBar"
      index: 0

#addl Power
- tapOn:
      id: "com.lenskart.app:id/text_left_power_selector"
      index: 1
#Low
- tapOn:
      id: "com.lenskart.app:id/progressBar"
      index: 0

#Number of boxes
- tapOn:
      id: "com.lenskart.app:id/text_right_power_selector"
      index: 2

- tapOn:
      id: "com.lenskart.app:id/progressBar"
      index: 1

#number of boxes
- tapOn:
      id: "com.lenskart.app:id/text_left_power_selector"
      index: 2

- tapOn:
      id: "com.lenskart.app:id/progressBar"
      index: 1