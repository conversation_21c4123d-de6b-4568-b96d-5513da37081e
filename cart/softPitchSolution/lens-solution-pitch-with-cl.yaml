appId: "com.lenskart.app"

env:
  ADD_SOLUTION: com.lenskart.app:id/btn_add_gold
---

# check if lens solution pitch is displaying on cart
- assertVisible:
    id: "com.lenskart.app:id/tv_offer_tag_line"

# check for add solution button
- assertVisible:
    id: ${ADD_SOLUTION}

# add solution
- tapOn:
    id: ${ADD_SOLUTION}

- waitForAnimationToEnd

# check product card added for lens solution by checking its sub title
- assertVisible:
    id: "com.lenskart.app:id/tv_product_description"
- assertVisible:
    text: "Solution"


