appId: "com.lenskart.app"
---

# check for sunglass product added in cart
- assertVisible:
    id: "com.lenskart.app:id/tv_power_view_similar"

- runFlow: power-details-bottomsheet.yaml

# saved power
#- runFlow: ../../addPower/add-power-saved-powers.yaml
- tapOn: "Saved Power"
- tapOn:
      id: "com.lenskart.app:id/parent_LL"
      index: 0

- runFlow: power-details-bottomsheet.yaml

# enter manually
- runFlow: ../../addPower/add-power-enter-manully.yaml

- runFlow: power-details-bottomsheet.yaml

# upload prescription
#- runFlow: ../../addPower/add-power-upload-prescription.yaml

#- runFlow: power-details-bottomsheet.yaml

# hto
- runFlow: ../../addPower/home-eye-test.yaml

#- runFlow: power-details-bottomsheet.yaml

# submit power later
#- runFlow: ../../addPower/add-power-submit-later.yaml