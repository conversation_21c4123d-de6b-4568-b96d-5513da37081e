appId: "com.lenskart.app"

env:
  SU<PERSON>IT_BOTTOMSHEET: "com.lenskart.app:id/tv_power_view_similar"
---

# tap on power text to open bottomsheet
- tapOn:
    id: ${SUBMIT_BOTTOMSHEET}

# tap on confirm to not change power
- runFlow:
    when:
      visible: "Submit Power Later"
    commands:
      - tapOn: "Submit Power Later"

# reopen submit power bottomsheet
- tapOn:
    id: ${SUBMIT_BOTTOMSHEET}

# submit now
- runFlow:
    when:
      visible: "Submit Now"
    commands:
      - tapOn:
          text: "submit Now"

- assertVisible:
    text: "Submit Power Later in 15 days"

- tapOn: "Submit Power Later in 15 days"