appId: com.lenskart.app
---

# tap on eyeglasses> men from home
- tapOn:
    below:
      id: "com.lenskart.app:id/recyclerview"
      index: 0
    id: "com.lenskart.app:id/image"
    index: 0

# choose brand jj
- tapOn:
    id: "com.lenskart.app:id/action_button_container"
    index: 0

#tap on first item on the plp
- tapOn:
    id: "com.lenskart.app:id/btn_view_details"
    index: 0

#tap on add to cart button
- tapOn:
    id: "com.lenskart.app:id/btn_add_to_cart"

# add lens type
- runFlow:
    file: ../power/with-power-eyeglass-anti-glare-premium.yaml

#submit power later
- runFlow:
    file: ../addPower/add-power-submit-later.yaml


