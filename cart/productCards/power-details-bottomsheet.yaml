appId: "com.lenskart.app"

env:
  POWER_BOTTOMSHEET: "com.lenskart.app:id/tv_power_view_similar"
---

# tap on power text to open bottomsheet
- tapOn:
    id: "com.lenskart.app:id/tv_power_view_similar"

# tap on confirm to not change power
- runFlow:
    when:
      visible: "Confirm"
    commands:
      - tapOn: "Confirm"

# reopen submit power bottomsheet
- tapOn:
    id: ${POWER_BOTTOMSHEET}

# change power
- runFlow:
    when:
      visible: "Change Power"
    commands:
      - tapOn:
          text: "Change Power"