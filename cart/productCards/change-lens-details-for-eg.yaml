appId: com.lenskart.app
---

# check for eyeglass product added in cart
- assertVisible:
    id: "com.lenskart.app:id/tv_sub_title"

- runFlow: lens-details-bottomsheet.yaml

# with Power changes
- runFlow: with-power-lens-changes-for-eg.yaml

- runFlow: lens-details-bottomsheet.yaml

# zero power changes
- runFlow: zero-power-changes.yaml

- runFlow: lens-details-bottomsheet.yaml

# reading power
- runFlow: ../../power/reading-power-eyeglass.yaml

- runFlow: lens-details-bottomsheet.yaml

# progressive
- runFlow: progressive-lens-changes.yaml

- runFlow: lens-details-bottomsheet.yaml

# bifocals
- runFlow: bifocal-lens-changes.yaml

- runFlow: lens-details-bottomsheet.yaml

# frame only
- tapOn: "Frame Only"