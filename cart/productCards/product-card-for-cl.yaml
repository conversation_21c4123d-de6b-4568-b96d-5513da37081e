appId: "com.lenskart.app"
---

# to check if CL product card is showing
- assertVisible:
    id: "com.lenskart.app:id/card_product"

# persuation tag
- assertVisible:
    id: "com.lenskart.app:id/tv_persuasion_tag"

# show power details
- tapOn: "Submit power later"
  #"com.lenskart.app:id/tv_power_view_similar"
- tapOn: "Submit Power Later"

# check CL quantity
- assertVisible:
      id: "com.lenskart.app:id/view_no_of_boxes_gradient"
- assertVisible:
      id: "com.lenskart.app:id/tv_left_box"
- assertVisible:
      id: "com.lenskart.app:id/tv_right_box"

# submit power for CL from cart
- runFlow: submit-cl-power.yaml

# check product images by opening product image gallery
- runFlow: product-image-gallery.yaml

# remove product card
- runFlow: remove-product.yaml

# add to wishlist
- runFlow: add-to-wishlist-from-cart.yaml
