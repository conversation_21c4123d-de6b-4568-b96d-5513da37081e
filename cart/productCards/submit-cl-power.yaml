appId: "com.lenskart.app"
---

# check for submit power later button
- assertVisible:
    #id: "com.lenskart.app:id/tv_power_view_similar"
    text: "Submit power later"
- runFlow: submit-power-later.yaml

# add saved power of CL from old power flow
# cart calls old flow of power types for cl
- tapOn:
      id: "com.lenskart.app:id/link_saved_prescription"
- tapOn:
      id: "com.lenskart.app:id/radio_button"
      index: 0
- tapOn:
      id: "com.lenskart.app:id/btn_submit"

