appId: "com.lenskart.app"
---

# to check if product card is showing
- assertVisible:
    id: "com.lenskart.app:id/card_product"

# check product images by opening product image gallery
- runFlow: product-image-gallery.yaml

# persuation tag
- assertVisible:
    id: "com.lenskart.app:id/tv_persuasion_tag"

# confirm or change lens type
- runFlow: change-lens-details-for-sg.yaml

# submit power later
- runFlow: submit-power-later.yaml

# confirm or change power type
- runFlow: change-power-details-for-sg.yaml

# remove product card
- runFlow: remove-product.yaml

# add to wishlist
- runFlow: add-to-wishlist-from-cart.yaml
