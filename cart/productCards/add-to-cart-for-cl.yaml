appId: "com.lenskart.app"
---

# Tap on search input field
- tapOn:
      id: "com.lenskart.app:id/search"
      optional: true

#goto search, search for cl pid
- inputText: "144127"
- tapOn:
      id: "com.lenskart.app:id/rvSearch"

- scrollUntilVisible:
    element:
      text: "I will submit power later"
    direction: DOWN

- runFlow: ../../clPowerType/c-l-i-will-submit-power-later.yaml


# skip adding solution
- tapOn:
      id: "com.lenskart.app:id/btn_skip_to_cart"
