appId: "com.lenskart.app"
---


# tap on lens type to open bottomsheet
- tapOn:
    id: "com.lenskart.app:id/tv_sub_title"


# tap on confirm to not change lens
- runFlow:
    when:
      visible: "Confirm"
    commands:
      - tapOn: "Confirm"

# reopen submit power bottomsheet
- tapOn:
    id: "com.lenskart.app:id/tv_sub_title"

# submit now
- runFlow:
    when:
      visible: "Change Lens"
    commands:
      - tapOn:
          text: "Change Lens"