appId: com.lenskart.app
---

# tap on product image on product card on cart screen
- tapOn:
    id: "com.lenskart.app:id/iv_product"

# validate product image gallery screen
- assertVisible:
      id: "com.lenskart.app:id/cv_container"


# scroll images in product gallery
- repeat:
      times: 5
      commands:
          - swipe:
                direction: LEFT

# to tap on view more details
- tapOn:
    id: "com.lenskart.app:id/btn_view_more_details"

# come back to cart
- back

- tapOn:
      id: "com.lenskart.app:id/iv_product"

# to close product image gallery
- tapOn:
   id: "com.lenskart.app:id/iv_cross"




