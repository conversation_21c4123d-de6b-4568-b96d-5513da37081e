appId: "com.lenskart.app"
---

# select lens to brown tinted power lenses
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Brown tinted Power Lenses"
- tapOn: "Submit Power Later"

- runFlow: lens-details-bottomsheet.yaml

# Grey tinted Power Lenses
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Brown tinted Power Lenses"
- tapOn: "Free Home Eye Test"

- runFlow: lens-details-bottomsheet.yaml


# Green tinted Power Lenses
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Green tinted Power Lenses"
- tapOn: "Submit Power Later"

- runFlow: lens-details-bottomsheet.yaml

# Blue tinted Power Lenses
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Blue tinted Power Lenses"
- tapOn: "Submit Power Later"


# Pink tinted
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Pink Tinted "
- tapOn: "Submit Power Later"

# Yellow tinted
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Yellow Tinted "
- tapOn: "Submit Power Later"
