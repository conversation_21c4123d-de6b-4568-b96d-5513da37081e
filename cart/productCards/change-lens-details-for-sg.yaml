appId: com.lenskart.app
---

# check for sunglasses product added in cart
- assertVisible:
    id: "com.lenskart.app:id/tv_sub_title"

- runFlow: lens-details-bottomsheet.yaml

# with Power changes

# brown tinted
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Brown tinted Power Lenses"
- tapOn: "Submit Power Later in 15 days"

- runFlow: lens-details-bottomsheet.yaml

# Grey tinted
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Grey tinted Power Lenses"
- tapOn: "Submit Power Later in 15 days"

- runFlow: lens-details-bottomsheet.yaml

# green tinted
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Green tinted Power Lenses"
- tapOn: "Submit Power Later in 15 days"

- runFlow: lens-details-bottomsheet.yaml

# blue tinted
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Blue tinted Power Lenses"
- tapOn: "Submit Power Later in 15 days"

- runFlow: lens-details-bottomsheet.yaml

# pink tinted
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Pink tinted"
- tapOn: "Submit Power Later in 15 days"

- runFlow: lens-details-bottomsheet.yaml

# yellow tinted
- runFlow:
    file: ../../power/optimizes-sunglasses-with-power.yaml
    env:
      lensType: "Yellow tinted"
- tapOn: "Free Home Eye Test"

- runFlow: lens-details-bottomsheet.yaml


# without Power
- runFlow:
    file: ../../power/without-power-sunglass.yaml