appId: "com.lenskart.app"
---

# anti-glare normal corridor progressive
- runFlow: ../../progressive/progressive-eyeglass-anti-glare-normal-corridor.yaml
- tapOn: "Submit Power Later"

- runFlow: lens-details-bottomsheet.yaml

# BLU Screen normal corridor progressive without coating
- runFlow: ../../progressive/progressive-eyeglass-blu-screen-normal-corridor.yaml
- tapOn: "Skip coating addition"
- tapOn: "Submit Power Later"

- runFlow: lens-details-bottomsheet.yaml

#check if coating is available
- assertVisible:
    text: "Add Coatings:"

# BLU Screen normal corridor progressive with coating
- runFlow: ../../progressive/progressive-eyeglass-blu-screen-normal-corridor.yaml
- tapOn: "Photochromatic BLU Coating for BLU Progressive"
- tapOn: "Free Home Eye Test"

- runFlow: lens-details-bottomsheet.yaml

- scroll

# BLU Screen normal corridor progressive with coating
- runFlow: ../../progressive/progressive-eyeglass-blu-screen-normal-corridor.yaml
- tapOn: "Photochromatic BLU Coating for BLU Progressive"
- tapOn: "Submit Power Later"

- runFlow: lens-details-bottomsheet.yaml

- scroll

# BLU Screen normal corridor progressive without coating
- runFlow: ../../progressive/progressive-eyeglass-blu-screen-wide-corridor.yaml
- tapOn: "Skip coating addition"
- tapOn: "Submit Power Later"

- runFlow: lens-details-bottomsheet.yaml

- scroll

# BLU Screen normal corridor progressive without coating
- runFlow: ../../progressive/progressive-eyeglass-blu-screen-wide-corridor.yaml
- tapOn: "Photochromatic Coating for Wide Progressive"
- tapOn: "Submit Power Later"




