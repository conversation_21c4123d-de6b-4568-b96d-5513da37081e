appId: "com.lenskart.app"
---

# cart header
# to check if cart is not empty
- assertVisible:
    id: "com.lenskart.app:id/tv_toolbar_title"

# let's remove products to empty cart
- repeat:
    while:
      notVisible:
        text: "items"
        #id: "com.lenskart.app:id/tv_toolbar_title"
    commands:
      - runFlow:
          file: productCards/remove-product.yaml

- assertVisible:
    text: "Cart"
