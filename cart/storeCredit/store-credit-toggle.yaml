appId: com.lenskart.app
---

- scrollUntilVisible:
    element:
        id: "com.lenskart.app:id/title_store_credit"
    direction: DOWN
    #timeout: 40000
    #speed: 50 #0-100
    visibilityPercentage: 100
    centerElement: true

# toggle will apply or remove sc, so using same file for both actions
- tapOn:
    id: "com.lenskart.app:id/toggle_apply_remove_sc"

- runFlow: billDetails/show-store-credit-in-bill-details.yaml