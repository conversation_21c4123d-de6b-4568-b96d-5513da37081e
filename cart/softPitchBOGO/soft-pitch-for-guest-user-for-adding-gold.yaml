appId: "com.lenskart.app"
---

# Add a product without logging the usre
#- runFlow:
 #   file: ../cart/addToCartForGuestUser.yaml

# soft pitch for gold user will appear after adding a single product

#tap on i icon to open T&C bottomsheet
- tapOn:
    id: "com.lenskart.app:id/tv_benefit_title"

#close bottomsheet
- tapOn:
    id: "com.lenskart.app:id/iv_close"


# tap on add gold button to add gold
- tapOn:
    id: "com.lenskart.app:id/btn_add_gold"

# Optional: wait if screen updates with delay
- waitForAnimationToEnd

# to check membership benefits - tap on arrow icon
- runFlow:
      file: membership-bottomsheet.yaml


