appId: "com.lenskart.app"
---

# add SG in cart
- runFlow: ../productCards/add-to-cart-for-sg.yaml

# check for SG product card
- runFlow: ../productCards/product-card-for-sg.yaml

# check if BOGO Pitch is showing after adding SG
- assertVisible:
    text: "EXCLUSIVE FOR YOU"

# add 2nd free SG
- runFlow: ../softPitchBOGO/buy-2nd-now-for-repeat-user.yaml
- tapOn:
    text: "Sunglasses"
- tapOn:
    id: "com.lenskart.app:id/image_gallery"
    index: 0
- tapOn: "Add to Cart"
- tapOn: "Without Power"



