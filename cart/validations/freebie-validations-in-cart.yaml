appId: "com.lenskart.app"
---

# to check if freebie is present or applicable in cart
- assertVisible:
    text: "Free gift for you!"


# CLAIMED STATE VALIDATION

#   already applied in cart - i.e., in unlocked + claimed state
- assertVisible:
    id: "com.lenskart.app:id/text_remove"

#verify green tick icon of freebie in applied/claimed state
- assertVisible:
    id: "com.lenskart.app:id/lottie_icon_gift"


# REMOVE FREEBIE
- runFlow: ../freebie/remove-freebie.yaml


# UNCLAIMED STATE VALIDATION

# freebie is available, unlocked but not claimed
- assertVisible:
    text: "Claim"

# claim or add freebie
- runFlow: ../freebie/claim-freebie.yaml

- waitForAnimationToEnd


# cancel removing freebie
- runFlow: ../freebie/cancel-removing-freebie.yaml


# PRICE = FREE (always)
- assertVisible:
    id: "com.lenskart.app:id/tv_lenskart_price"


# check expired freebie
#- runFlow: ../freebie/expired-freebie.yaml

