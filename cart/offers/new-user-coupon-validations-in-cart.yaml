appId: "com.lenskart.app"
---

# add EG in cart
- runFlow: ../productCards/add-to-cart-for-eg.yaml

#scroll to see offers
- scrollUntilVisible:
    element:
      text: "Offers"
    direction: DOWN

# check if offer section is present on cart
- assertVisible:
    text: "Offers"

# check if priority gv is visible on cart
- assertVisible:
    id: "com.lenskart.app:id/tv_sub_label_coupon"

# check if coupon: SINGLE is auto-applied after adding a product
- assertVisible:
    id: "com.lenskart.app:id/btn_remove"

- scrollUntilVisible:
      direction: UP
      element:
        id: "com.lenskart.app:id/card_product"

# remove product
- runFlow: ../productCards/remove-product.yaml

- back
- back
- back

# add accessory in cart so that no priority gv is shown on cart
# i.e., Appy Coupon widget is shown
- runFlow: ../productCards/add-to-cart-for-accessories.yaml

# check if apply coupon widget is visible
- assertVisible:
    id: "com.lenskart.app:id/tv_label_coupon"


