appId: "com.lenskart.app"
---

# this is for repeat user having both lk cash and coupon

# add EG in cart
- runFlow: ../productCards/add-to-cart-for-eg.yaml

#scroll to see offers
- scrollUntilVisible:
    element:
      text: "Offers"
    direction: DOWN

# check if offer section is present on cart
- assertVisible:
    text: "Offers"

# check if coupon is present on the cart
- assertVisible:
    id: "com.lenskart.app:id/tv_sub_label_coupon"

# apply coupon
- runFlow: ../offers/apply-coupon-from-cart.yaml

# remove coupon
- runFlow: ../offers/remove-coupon.yaml

# check if coupon is removed
- assertNotVisible:
    id: "com.lenskart.app:id/apply_coupon_tv"

# apply coupon
- runFlow: ../offers/apply-coupon-from-cart.yaml

# check if view all coupons is visible
- runFlow: ../offers/view-all-coupons.yaml

# manual apply coupon byt inputting gv
- runFlow: ../offers/manual-apply-coupon-from-coupons-screen.yaml

# check if coupons' list is present on coupon screen
- assertVisible:
    text: "Best offers for you"

# Apply coupon from coupons screen
- runFlow: ../offers/apply-coupon-from-coupons-screen.yaml

# check T&C of listed coupons
- runFlow: ../offers/open-coupon-terms-and-conditions.yaml
- runFlow: ../offers/close-coupon-terms-and-conditions.yaml

# check bank offers on coupons screen
- runFlow: ../offers/bank-offers.yaml

# back to cart
- back

# check if LK Cash is present in cart
- assertVisible:
    id: "com.lenskart.app:id/tv_label_lk_cash"

# apply LK Cash when coupon is applied,  bottomsheet gets opened
- runFlow: ../offers/remove-coupon-apply-lk-cash.yaml

# check if LK Cash is applied
- assertVisible:
    text: "LK cash applied"

# apply coupon when lk cash is applied
- runFlow: ../offers/remove-lk-cash-apply-coupon.yaml

# check if coupon is applied
- assertVisible:
    id: "com.lenskart.app:id/apply_coupon_tv"

