appId: "com.lenskart.app"

---

# what is Merge cart?
  # when guest user logins with a repeat user account which has existing items in its cart,
  # then after login, all the items gets merged
   # i.e. items of guest user cart + items of existing cart of repeat user


- runFlow: login-to-proceed-for-payments-from-guest-user-cart.yaml

# wait for lottie or success popup animations to end
- waitForAnimationToEnd

- runFlow: billDetails/showBillDetails.yaml
