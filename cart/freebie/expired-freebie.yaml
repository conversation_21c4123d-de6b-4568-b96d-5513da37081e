appId: com.lenskart.app
---

- assertVisible:
      id: "com.lenskart.app:id/text_timer"


# about to expire freebie
- assertVisible:
      text: "Hurry! Free gift expired soon"

# wait until last 10 seconds are running before timer stops
- extendedWaitUntil:
      visible: "Hurry! Free gift expired soon"
      timeout: 10000

#freebie expired U<PERSON> will show after timers is off
- assertVisible:
    id: "com.lenskart.app:id/card_expired"




