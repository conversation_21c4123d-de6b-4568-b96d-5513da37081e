appId: com.lenskart.app

---
#verifying user is on pdp
- assertVisible:
    id: "com.lenskart.app:id/image"

#Scrolling down till Bitz widget
- scroll:
          direction: DOWN
          visible: "com.lenskart.app:id/text_title"

#verifying charm bitz widget showing or not
- assertVisible:
    id: "com.lenskart.app:id/text_title"
    index: 2

#tapping on View All
- tapOn: "View All"

#verifying bottomsheet is coming or not
- assertVisible:
    id: "com.lenskart.app:id/tv_title"

#verifying first bitz is visible
- assertVisible:
    id: "com.lenskart.app:id/img_bits"
    index: 0

#adding first bitz
- tapOn:
    text: "Add"
    index: 0

#verifying third bitz is visible
- assertVisible:
    id: "com.lenskart.app:id/img_bits"
    index: 2

#adding third bitz
- tapOn:
    text: "Add"
    index: 2

#tapping on Select lens package
- tapOn: "Select Lens Package"



