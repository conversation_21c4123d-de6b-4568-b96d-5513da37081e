appId: com.lenskart.app

---
#verifying user is on pdp
- assertVisible:
    id: "com.lenskart.app:id/image"

#Scrolling down till Bitz widget
- scrollUntilVisible:
    element:
      text: "View All"
    direction: DOWN
#verify charm bits widget is visible
- assertVisible: "Choose Charm Bits ( 1st FREE)"

#selecting first bitz
- tapOn:
    id: "com.lenskart.app:id/bits_selector"
    index: 0

#selecting second bitz
- tapOn:
    id: "com.lenskart.app:id/bits_selector"
    index: 1

#tapping on Select lenses button
- tapOn:
    id: "com.lenskart.app:id/btn_add_to_cart"
