appId: com.lenskart.app

---

#verifying user is on pdp
- assertVisible:
    id: "com.lenskart.app:id/image"

#Scrolling down till Bitz widget
- scrollUntilVisible:
      element:
          text: "Select Charm Bitz"
      direction: DOWN

#click on select charm bitz button
- tapOn: "Select Charm Bitz"


#adding last bitz option
- tapOn:
    text: "Add"
    index: 0

#selecting second bitz which is third last option
- tapOn:
    text: "Add"
    index: 1

#tapping on Select lens package button
- tapOn: "Select Lens Package"

